{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "typescript"], "eslint.useFlatConfig": true, "typescript.tsdk": "node_modules/typescript/lib", "typescript.preferences.importModuleSpecifier": "non-relative", "sonarlint.connectedMode.project": {"connectionId": "discord-interchat", "projectKey": "Discord-InterChat_InterChat"}, "codescene.previewCodeHealthMonitoring": true}