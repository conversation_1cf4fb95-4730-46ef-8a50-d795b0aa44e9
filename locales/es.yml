rules:
  header: 'Reglas de InterChat'
  botRulesNote: 'Estas reglas existen para garantizar una experiencia segura y agradable para todos. Léelas con atención y respétalas:'
  rules: |
    1. **No discurso de odio ni acoso**
    -# > **Incluye:** Usar insultos o lenguaje de odio para atacar a otros, [y más]({guidelines_link}).
    2. **No contenido ilegal**
    -# > **Incluye:** Compartir enlaces a contenido ilegal, Fomentar la violencia, [y más]({guidelines_link}).
    3. **No contenido NSFW extremo ni gore**
    -# > **Incluye:** Publicar gore o gore extremo en InterChat, Publicar contenido sexual en hubs no NSFW, [y más]({guidelines_link}).
    4. **No spam ni flood**
    -# > **Incluye:** Envío masivo de spam o bots de flood, [y más]({guidelines_link}).
    5. **No suplantación de identidad ni fraude**
    -# > **Incluye:** Hacerse pasar por el personal de InterChat o moderadores de hubs, Ejecutar estafas de criptomonedas o NFT, [y más]({guidelines_link}).
    6. **No explotación ni abuso**
    -# > **Incluye:** Grooming o comportamiento depredador hacia menores, Compartir, Solicitar, Chantajear o amenazar para Fomentar autolesiones, [y más]({guidelines_link}).
    7. **No compartir software malicioso**
    -# > **Incluye:** Compartir malware, virus, enlaces de 'nitro gratis', scripts dañinos, [y más]({guidelines_link}).

    También aceptas seguir los [Términos de servicio de Discord](https://discord.com/terms) y las [Normas de la comunidad](https://discord.com/guidelines). Consulta la [lista completa de reglas]({guidelines_link}).
  welcome: |
    {emoji} ¡Hola {user}! ¿Listo para conectarte con otras comunidades?
    Antes de que comiences a chatear entre servidores, repasemos rápidamente nuestras normas comunitarias.
    Estas ayudan a que InterChat sea un gran lugar para todos.
  alreadyAccepted: '{emoji} Ya has aceptado las reglas. Ahora puedes usar InterChat al máximo.'
  continue: Continuar
  accept: Aceptar
  decline: Rechazar
  agreementNote: Al aceptar estas reglas, aceptas seguirlas mientras uses InterChat. Incumplirlas puede resultar en restricciones o prohibiciones.
  hubAgreementNote: |
    Al aceptar estas reglas, aceptas seguirlas mientras chateas en este centro.
    Incumplirlas puede resultar en tu expulsión del centro.

    ⚠️ **No puedes enviar mensajes en este centro hasta que aceptes estas reglas.**
  accepted: |
    {emoji} ¡Todo listo! Bienvenido a la comunidad de InterChat.

    {emoji} ¡Todo listo! Bienvenido a la comunidad de InterChat.

    ### Empieza rápido:
    - Visita `/hub browse` para encontrar comunidades activas
    - Únete a discusiones que te interesen o crea tu propia comunidad a través de nuestro __dashboard__
    - Usa nuestro nuevo comando `/call` para conectarte con servidores individuales

    ¿Necesitas ayuda? Únete a nuestra [comunidad]({support_invite}) - ¡estaremos encantados de ayudarte! Por favor, considera apoyarnos [donando]({donateLink}) para que InterChat siga creciendo y prosperando.
  declined: |
    {emoji} Has rechazado las reglas de InterChat.
    -# ⚠️ **No podrás usar InterChat ni chatear con otros servidores hasta que aceptes las reglas.**
    -# Para intentarlo de nuevo, envía otro mensaje o usa cualquier comando de InterChat.
  hubAccepted: |
    {emoji} Has aceptado las reglas del centro.
    ¡Ahora puedes comenzar a chatear en este centro!
  hubDeclined: |
    {emoji} Has rechazado las reglas dé {hubName}.
    -# ⚠️ **No podrás enviar mensajes en este centro hasta que aceptes sus reglas.**
    -# Para intentarlo dé nuevo, envía otro mensaje en este centro.
  noHubRules: Este centro aún no ha establecido reglas específicas. Sin embargo, siguen aplicando las [reglas generales de InterChat]({rules_link}).
  hubRules: Reglas del centro
  viewbotRules: "Ver Reglas del Bot"
vote:
  description: |
    ¡Ayuda a más comunidades a descubrir InterChat! Tu voto en top.gg:
    - Ayuda a otros a encontrar comunidades activas
    - Desbloquea funciones especiales para ti
    - Apoya nuestro desarrollo independiente
  footer: "Los votos se actualizan cada 12 horas • ¡Gracias por apoyar InterChat!"
  button:
    label: "Vota en top.gg"
  perks:
    moreComingSoon: "¡Más beneficios próximamente! Sugiere algunos en el [servidor de soporte]({support_invite})."
network:
  accountTooNew: '{emoji} {user} Su cuenta es demasiado nueva para enviar mensajes usando InterChat. Por favor, inténtelo de nuevo más tarde.'
  deleteSuccess: '{emoji} El mensaje de {user} se ha eliminado de __**{deleted} de {total}**__ servidores.'
  editInProgress: '{emoji} Your request has been queued. Messages will be edited shortly...'
  editInProgressError: '{emoji} This message is already being edited by another user.'
  emptyContent: '{emoji} Message content cannot be empty.'
  newMessageContent: 'New Message Content'
  editMessagePrompt: '{emoji} Please use the modal to edit your message.'
  editSuccess: '{emoji} El mensaje de {user} se ha editado en __**{edited} de {total}**__ servidores.'
  onboarding:
    embed:
      title: '👋 ¡Bienvenido a {hubName}!'
      description: |
        ¡Has encontrado un hub comunitario activo! Antes de unirte a la discusión, echa un vistazo rápido a nuestras normas para mantener conversaciones atractivas y amigables para todos.
      footer: Red InterChat | Versión {version}
    inProgress: "{emoji} {channel} ya está en proceso de configuración para unirse a un centro.\nPor favor, espera a que se complete la configuración o cancélala si fuiste quien la inició."
blacklist:
  success: '{emoji} ¡**{name}** ha sido agregado con éxito a la lista negra!'
  removed: '{emoji} ¡**{name}** ha sido eliminado con éxito de la lista negra!'
  modal:
    reason:
      label: Razón
      placeholder: Razón de la lista negra
    duration:
      label: Duración
      placeholder: 'Duración de la lista negra. Por ejemplo: 1d, 1w, 1m, 1y. Deje en blanco para que sea permanente.'
  user:
    cannotBlacklistMod: '{emoji} No puedes poner en la lista negra a un moderador. Por favor, elimina primero su rol de moderador.'
    alreadyBlacklisted: '{emoji} Este usuario ya está en la lista negra.'
    easterEggs:
      blacklistBot: No puedes incluirme en la lista negra wtf.
  server:
    alreadyBlacklisted: '{emoji} Este servidor ya está en la lista negra.'
    unknownError: Error al agregar a la lista negra a **{server}**. Consulte a los desarrolladores para obtener más información.
  list:
    user: |
      **ID de Usuario:** {id}
      **Moderador:** {moderator}
      **Razón:** {reason}
      **Expira:** {expires}
    server: |
      **Id del Servidor:** {id}
      **Moderador:** {moderator}
      **Razón:** {reason}
      **Expira:** {expires}
msgInfo:
  buttons:
    message: Información del mensaje
    server: Información del servidor
    user: Información del usuario
    report: Reportar
  report:
    notEnabled: '{emoji} Este centro no tiene habilitado la acción de reportar.'
    success: '{emoji} Reporte enviado correctamente. ¡Gracias!'
invite: |
  Gracias por elegir invitar a InterChat. ¡Haga clic en el botón de abajo para invitarme!

  **[{invite_emoji} `Enlace de invitación`]({invite}) [{support_emoji} `Servidor de soporte`]({support})**
connection:
  joinRequestsDisabled: '{emoji} Las solicitudes de unión están desactivadas para este centro.'
  notFound: '{emoji} Conexión no válida. Verifique el ID del canal o seleccione de las opciones mostradas.'
  channelNotFound: '{emoji} No se puede encontrar el canal conectado. Para hablar nuevamente, elija un nuevo canal.'
  alreadyConnected: '{emoji} El canal {channel} ya está conectado a un centro.'
  switchChannel: '{emoji} Seleccione un canal para cambiar a usar el menú Seleccionar a continuación:'
  switchCalled: '{emoji} Interruptor de canal llamado, use el comando nuevamente para ver una nueva conexión.'
  switchSuccess: '{emoji} El canal ha sido conmutado. Ahora está conectado desde **{channel}**.'
  inviteRemoved: '{emoji} Invitación del servidor eliminada para este centro.'
  setInviteError: "{emoji} No se pudo crear la invitación.  \nPor favor, concédeme el permiso de `Crear Invitación` para el canal conectado."
  inviteAdded: '{emoji} Invitación añadida. Ahora otros pueden unirse a este servidor usando el comando `Apps > Información del mensaje/Reportar` y el comando `/joinrequest`.'
  emColorInvalid: '{emoji} Color no válido. Asegúrese de haber ingresado un código de color hexadecimal válido.'
  emColorChange: '{emoji} Se estableció el color exitosamente: {action}'
  embed:
    title: Detalles de la conexión
    fields:
      hub: Centro
      channel: Canal
      invite: Invitar
      connected: Conectada
      emColor: Color del embed
      compact: Modo compacto
    footer: Usa el menú desplegable de abajo para gestionar tu conexión.
  selects:
    placeholder: '🛠️ Seleccione una opción a continuación para administrar su conexión'
  unpaused:
    desc: |
      ### {tick_emoji} Conexión sin aliento

      Conexión no puesta para {channel}! Los mensajes del hub comenzarán a entrar al canal y puede enviar mensajes al hub nuevamente.
    tips: |
      **💡 Tip:** Use {pause_cmd} para pausar la conexión o {edit_cmd} para configurar los colores de embed, invitar a tu servidor y más.
  paused:
    desc: |
      ### {clock_emoji} Conexión pausada
      Conexión pausada para {channel}! Los mensajes del centro ya no entrarán en el canal y sus mensajes no se les transmitirá.
    tips: |
      **💡 consejo:** Use {unpause_cmd} para despausar la conexión o {leave_cmd} para dejar de recibir mensajes permanentemente.
hub:
  notFound: '{emoji} No se puede encontrar hub. Asegúrese de haber ingresado el nombre correcto del concentrador.'
  notFound_mod: '{emoji} No se puede encontrar hub. Asegúrese de haber ingresado el nombre correcto del concentrador y de que usted sea el propietario o un moderador del hub.'
  notManager: '{emoji} Debes ser un administrador del hub para realizar esta acción.'
  notModerator: '{emoji} Necesitas ser un moderador del hub para realizar esta acción.'
  notPrivate: '{emoji} Este hub no es privado.'
  notOwner: '{emoji} Solo el propietario de este hub puede realizar esta acción.'
  alreadyJoined: "Parece que hubo un problema con mi respuesta anterior. Déjame intentarlo de nuevo:\n\n{emoji} ¡Ya te has unido a otro hub **{hub}** desde {channel}! Usa `/disconnect` en ese hub y luego intenta nuevamente con `/connect`."
  invalidChannel: '{emoji} El canal no es válido. ¡Solo se admiten los canales de texto e hilo!'
  invalidImgurUrl: '{emoji} URL de imagen no válida para icono o banner. Asegúrese de haber ingresado una URL de imagen de Imgur válida que no es una galería o álbum.'
  join:
    success: |
      ¡Unido exitosamente al hub **{hub}** desde {channel}! Ahora puedes chatear con miembros de otros servidores desde este canal.
      - Usa `/connection` para explorar varias opciones de personalización para esta conexión.
      - Usa `/disconnect` para dejar de recibir mensajes de este hub.
      - Usa **`/connection edit`** para cambiar de canal.
  servers:
    total: 'Servidores conectados actuales: {from}-{to} / **{total}**'
    noConnections: '{emoji} Ningún servidor se ha unido a este centro todavía. Use `/connect` para unirse a este centro.'
    notConnected: "{emoji} Ese servidor no es parte de **{hub}**."
    connectionInfo: |
      ID del servidor: {serverId}
      Canal: #{channelName} `({channelId})`
      Unido a: {joinedAt}
      Invitación: {invite}
      Conectado: {connected}
  blockwords:
    deleted: '{emoji} ¡Regla contra palabrotas eliminada con éxito!'
    notFound: '{emoji} Regla contra palabrotas no encontrada.'
    maxRules: '{emoji} Has alcanzado el número máximo de reglas contra palabrotas (2) para este hub. Por favor, elimina una regla antes de añadir otra.'
    configure: 'Configura las acciones para la regla: {rule}'
    actionsUpdated: '{emoji} Se han actualizado las acciones a tomar por la regla. **Nuevas acciones:** {actions}'
    selectRuleToEdit: Selecciona una regla para editar sus palabras/acciones
    listDescription: |
      ### {emoji} Reglas contra palabrotas 
      Este hub tiene {totalRules}/2 reglas contra palabrotas configuradas.
    listFooter: Selecciona una regla usando el menú para ver sus detalles completos.
    ruleDescription: |
      ### {emoji} Editando regla: {ruleName}{words}
    ruleFooter: '¡Haz clic en el botón de abajo para editar las palabras o el nombre de la regla!'
    actionSelectPlaceholder: 'Selecciona las acciones que esta regla debería realizar.'
    embedFields:
      noActions: '{emoji} **¡Ninguna!** Configura usando el menú de abajo.'
      actionsName: 'Acciones configuradas:'
      actionsValue: '{actions}'
    modal:
      addRule: Añadir regla contra palabrotas
      editingRule: 'Advertido por: {moderator}'
      ruleNameLabel: Nombre de la regla
      wordsLabel: 'Palabras'
      wordsPlaceholder: 'Palabras separadas por comas. (Usa * como comodín). Ej.: palabra1, *palabra2*, *palabra3, palabra4*'
    validating: '{emoji} Validando regla contra palabrotas...'
    noRules: |
      ### {emoji} ¡Establezcamos algunas reglas contra las palabrotas!
      Usa el botón `Agregar regla` para crear una.
  create:
    modal:
      title: Crear centro
      name:
        label: Nombre
        placeholder: Ingrese un nombre para su centro.
      description:
        label: Descripción
        placeholder: Ingrese una descripción para su centro.
      icon:
        label: URL de icono
        placeholder: Ingrese una URL de imagen Imgur.
      banner:
        label: URL de banner
        placeholder: Ingrese una URL de imagen Imgur.
    maxHubs: '{emoji} [Vota por InterChat]({voteUrl}) para crear más hubs. Has alcanzado el número máximo de hubs ({maxHubs}) que puedes crear.'
    invalidName: '{emoji} Nombre de la concentración no válida. No debe contener `discord`,`clyde` o `/`. Elija otro nombre.'
    nameTaken: '{emoji} Este nombre del centro ya está tomado. Elija otro nombre.'
    success: |
      ## ¡Centro creado! Está __private__ por defecto. 
      Usa `/hub edit hub:{name}` para personalizar tu hub. Sigue los pasos a continuación para comenzar: 
      ### Próximos pasos: 
      1. **Crear una invitación:** 
      > Usa `/hub invite create` para generar una invitación y permitir que otros se unan. 
      2. **Vincula un canal:** 
      > Usa `/connect` **con el enlace de invitación generado previamente** para vincular un canal al hub y empezar a chatear. 
      3. **Configurar el hub:** (Recomendado) 
      > Usa `/hub config settings`, `/hub config logging` y `/hub config anti-swear` para ajustar la configuración del hub. 
      4. **Agregar moderadores:** 
      > Usa `/hub moderator add` para asignar moderadores al hub. 
      5. **Personalizar el hub:** 
      > Usa `/hub edit` para cambiar el ícono, el banner y la descripción del hub. 
      6. **Hacerlo público:** 
      > Usa `/hub visibility` para hacer el hub público y permitir que otros lo exploren y se unan sin necesidad de una invitación. (Opcional) 

      Si tienes preguntas o necesitas ayuda, no dudes en preguntar en el [servidor de soporte]({support_invite}). Considera [donar]({donateLink}) para ayudar a cubrir los costos de desarrollo.
  delete:
    confirm: '¿Estás seguro de que desea eliminar **{hub}**? Esta acción es irreversible. Todos los servidores conectados, invitaciones y datos de mensajes se eliminarán de este centro.'
    ownerOnly: '{emoji} Solo el propietario de este centro puede eliminarlo.'
    success: '{emoji} El centro **{hub}** se ha eliminado.'
    cancelled: '{emoji} La eliminación de concentración ha sido cancelada.'
  browse:
    joinConfirm: |
      ¿Estás seguro qué deseas unirte a {hub} dé {channel}?

      **Nota:** Siempre puede cambiar esto más tarde usando `/connection`.
    joinFooter: '¿Quieres usar un canal diferente? Use el menú desplegable a continuación.'
    noHubs: '{emoji} No hay centros enumerados aquí en este momento. ¡Por favor, inténtelo de nuevo más tarde!'
    rating:
      invalid: Calificación inválida. Debe ingresar un número entre 1 y 5.
      success: '¡Calificación enviada! Gracias por tus comentarios.'
  invite:
    create:
      success: |
        ### Invitación creada!

        Su invitación ha sido creada con éxito. Otros ahora pueden unirse a este centro utilizando el comando `/connect`.

        - **Únete usando:** `/connect invite:{inviteCode}`
        - **Mira las invitaciones:** `/hub invite list`
        - **Expiración:** {expiry}
        - **Usos**: ∞

        **Nota:** Puedes revocar esta invitación usando `/hub invite revoke {inviteCode}`.
    revoke:
      invalidCode: '{emoji} Código de invitación no válido. Asegúrese de haber ingresado un código de invitación válido.'
      success: '{emoji} Invitación {inviteCode} revocada.'
    list:
      title: '**Códigos de invitación:**'
      noInvites: '{emoji} Este centro aún no tiene invitaciones. Use `/hub invite create` para crear uno.'
      notPrivate: '{emoji} Solo los centros privados pueden tener invitaciones. Use `/Hub Management` para que este centro sea privado.'
  joined:
    noJoinedHubs: '{emoji} Este servidor aún no se ha unido a ningún centro. Use `/hub browse` para ver una lista de centros.'
    joinedHubs: Este servidor es parte de **{total}** hub(s). Use `/disconnect` para dejar un centro.
  leave:
    noHub: '{emoji} Ese canal no es válido o no ha unido ningún centro.'
    confirm: '¿Estás seguro de que deseas dejar **{hub}** de {channel}? No se enviarán más mensajes a este servidor desde este centro.'
    confirmFooter: Confirme usando el botón a continuación en 10 segundos.
    success: '{emoji} Nos fuimos del centro en {channel}. No se enviarán más mensajes a este servidor desde este centro. Puede reunirse usando `/connect`.'
  moderator:
    noModerators: '{emoji} Este centro todavía no tiene moderadores. Use el comando `/hub moderator add` para agregar uno.'
    add:
      success: '{emoji} **{user}** se ha agregado como moderador en posición **{position}**.'
      alreadyModerator: '{emoji} **{user}** ya es un moderador.'
    remove:
      success: '{emoji} **{user}** se ha eliminado como moderador.'
      notModerator: '{emoji} **{user}** no es un moderador.'
      notOwner: '{emoji} Solo el propietario de este centro puede eliminar un administrador.'
    update:
      success: "{emoji} **{user}** La posición se ha actualizado a **{position}**."
      notModerator: "{emoji} **{user}** no es un moderador."
      notAllowed: "{emoji} Solo los administradores de centros pueden actualizar la posición de un moderador."
      notOwner: "{emoji} Solo el propietario de este centro puede actualizar la posición de un administrador."
  manage:
    dashboardTip: "**🛠️ NUEVO Dashboard:** ¡Interfaz mejorada y más funciones! Pruébalo en la [página de Dashboard de tu centro]({url})."
    enterImgurUrl: Ingrese una URL de imagen de Imgur válida que no sea una galería o álbum.
    icon:
      changed: El icono del centro cambió con éxito.
      modal:
        title: Editar Icono
        label: URL de icono
      selects:
        label: Editar Icono
        description: Cambie el icono de este centro.
    description:
      changed: La descripción del concentrador cambió con éxito.
      modal:
        title: Editar Descripción
        label: Descripción
        placeholder: Ingrese una descripción para este centro.
      selects:
        label: Cambiar Descripción
        description: Cambie la descripción de este centro.
    banner:
      changed: El banner del hub se cambió con éxito.
      removed: Banner de concentrador eliminado con éxito.
      modal:
        title: Editar Banner
        label: URL de banner
      selects:
        label: Editar Banner
        description: Cambia la pancarta de este centro.
    visibility:
      success: '{emoji} La visibilidad del centro cambió correctamente a **{visibility}**.'
      selects:
        label: Cambiar la visibilidad
        description: Haz este centro público o privado.
    toggleLock:
      selects:
        label: 'Bloquear/Desbloquear Centro'
        description: 'Bloquea o desbloquea los chats del centro'
      confirmation: 'Los chats de centro ahora son {status}.'
      announcementTitle: 'Los chats de centro ahora son {status}.'
      announcementDescription:
        locked: 'Solo los moderadores pueden enviar mensajes.'
        unlocked: 'Cualquiera pueden enviar mensajes.'
    embed:
      visibility: 'Visibilidad'
      connections: 'Conexiones'
      chatsLocked: 'Chats Bloqueados'
      blacklists: 'Listas negras'
      total: 'Total'
      users: 'Usuarios'
      servers: 'Servidores'
      hubStats: 'Estadísticas del Centro'
      moderators: 'Moderadores'
      owner: 'Propietario'
    logs:
      title: Configurar registros
      reset: '{emoji} Se reinició con éxito la configuración de registros para `{type}`.'
      roleSuccess: '{emoji} registros de tipo `{type}` ahora mencionará {role}!'
      roleRemoved: '{emoji} Los registros de tipo `{type}` no volverán a mencionar un rol.'
      channelSuccess: '{emoji} registros de tipo `{type}` se enviará a {channel} de ahora!'
      channelSelect: '#️⃣ Seleccione el tipo de registro para configurar'
      roleSelect: '🏓 Seleccione el papel a mencionar cuándo se activa un registro.'
      reportChannelFirst: '{emoji} Establezca primero un canal de registro.'
      config:
        title: Configurar `{type}` registra
        description: |
          {arrow} Seleccione un canal de registro y/o rol que se haga ping desde el menú desplegable a continuación.
          {arrow} También puede deshabilitar el registro usando el botón a continuación.
        fields:
          channel: Canal
          role: Mención de roles
      reports:
        label: Informes
        description: Recibir informes de los usuarios.
      modLogs:
        label: Registros de mod
        description: Acciones de moderación de registro. (por ejemplo, la lista negra, el mensaje elimina, etc.)
      joinLeaves:
        label: Unir/salir
        description: Registre cuando un servidor se une o deja este centro.
      appeals:
        label: Apelaciones
        description: Reciba apelaciones de usuarios/servidores en la lista negra.
      networkAlerts:
        label: Alertas de la red
        description: Recibir alertas sobre mensajes bloqueados automáticamente.
  transfer:
    invalidUser: '{emoji} No se encontró el usuario especificado.'
    selfTransfer: '{emoji} No puedes transferirte la propiedad a ti mismo.'
    botUser: '{emoji} No puedes transferir la propiedad a un bot.'
    confirm: '¿Estás seguro de que quieres transferir la propiedad de **{hub}** a {newOwner}? Serás degradado al rol de administrador.'
    cancelled: '{emoji} La transferencia ha sido cancelada.'
    error: '{emoji} Se ha producido un error al transferir la propiedad del centro.'
    success: '{emoji} Se ha transferido con éxito la propiedad de **{hub}** a {newOwner}. Has sido añadido como administrador.'
    timeout: '{emoji} Se ha agotado el tiempo de espera de transferencia del centro.'
  rules:
    noRules: "{emoji} Este centro no tiene reglas configuradas aún. ¡Añadamos algunas!"
    list: "### {emoji} Reglas del centro\n{rules}"
    maxRulesReached: "{emoji} Se ha alcanzado el número máximo de reglas ({max})."
    ruleExists: "{emoji} Esta regla ya existe."
    selectedRule: "Regla seleccionada {number}"
    modal:
      add:
        title: Añadir regla al centro
        label: Contenido de la regla
        placeholder: Introduzca el contenido de la regla (máx. 1000 caracteres)
      edit:
        title: Editar regla del centro
        label: Contenido de la regla
        placeholder: Introduzca el nuevo contenido de la regla (máx. 1000 caracteres)
    select:
      placeholder: Seleccione una regla para editar o eliminar
      option:
        label: Regla {number}
    buttons:
      add: Añadir regla
      edit: Editar regla
      delete: Eliminar regla
      back: Volver
    success:
      add: '{emoji} ¡Regla añadida con éxito!'
      edit: '{emoji} ¡Regla actualizada con éxito!'
      delete: '{emoji} ¡Regla eliminada con éxito!'
    view:
      title: 'Regla {number}'
      select: Seleccione una acción para esta regla
  welcome:
    set: '{emoji} ¡Mensaje de bienvenida actualizado con éxito!'
    removed: '{emoji} Mensaje de bienvenida eliminado.'
    voterOnly: '{emoji} ¡Los mensajes de bienvenida personalizados son un beneficio de solo votantes! Vota para desbloquear esta función.'
    placeholder: |
      ¡Bienvenido {user} de {serverName} a {hubName}! 🎉
      Miembros: {memberCount}, Centro: {totalConnections}!!
report:
  modal:
    title: Detalles del reporte
    other:
      label: Detalles del reporte
      placeholder: Una descripción detallada del reporte.
    bug:
      input1:
        label: Detalles del error
        placeholder: Ej. Fallas de interacción frecuentes para /help command...
      input2:
        label: Descripción detallada (opcional)
        placeholder: Pasos que tomaste. Ej. 1. Ejecuta /help 2. Espera 5 segundos...
  reasons:
    spam: Spam o mensajes excesivos
    advertising: Anuncios no deseados o autopromoción
    nsfw: NSFW o contenido inapropiado
    harassment: Acoso u hostigamiento
    hate_speech: Discurso de odio o discriminación
    scam: Intento de estafa, fraude o phishing
    illegal: Contenido ilegal o actividades ilegales
    personal_info: Compartir información personal/privada
    impersonation: Suplantar la identidad de otros
    breaks_hub_rules: Violar las reglas del centro
    trolling: Trolear o interrupción intencional
    misinformation: Información falsa o engañosa
    gore_violence: Gore o violencia extrema
    raid_organizing: Organizar raideos o ataques
    underage: Usuario menor de edad o contenido de menores de edad
  dropdown:
    placeholder: Seleccione una razón para su reporte
  submitted: '{emoji} Reporte enviado con éxito. Únete a {support_command} para obtener más detalles. ¡Gracias!'
  bug:
    title: Informe de error
    affected: Componentes afectados
    description: Elija qué componente del bot con el que enfrenta problemas.
language:
  set: '¡Sé de idiomas! Ahora te responderé en **{lang}**.'
errors:
  messageNotSentOrExpired: '{emoji} Este mensaje no se envió en un centro, ha expirado o te faltan permisos para realizar esta acción.'
  notYourAction: "{emoji} Lo siento, no puedes realizar esta acción. Por favor ejecute el comando usted mismo."
  notMessageAuthor: '{emoji} No eres el autor de este mensaje.'
  commandError: |
    {emoji} Se produjo un error al ejecutar este comando. Se ha registrado a nuestro sistema. Si este problema persiste, únase a nuestro [servidor de soporte]({support_invite}) y repórtelo.
    ```JS
    {errorId}
    ```
  mustVote: Por favor [vote](https://top.gg/bot/769921109209907241/vote) para que InterChat use este comando, ¡su apoyo es muy apreciado!
  inviteLinks: '{emoji} No puedes enviar enlaces de invitación a este hub. Configura una invitación en `/connection` en su lugar. Los moderadores del hub pueden configurar esto usando `/hub edit settings`'
  invalidLangCode: '{emoji} Código de lenguaje no válido. Asegúrese de haber ingresado un [código de idioma] correcto (https://cloud.google.com/translate/docs/languages).'
  unknownServer: '{emoji} servidor desconocido. Asegúrese de haber ingresado correctamente la **ID del Servidor**.'
  unknownNetworkMessage: '{emoji} Mensaje desconocido. Si se ha enviado en el último minuto, espere unos segundos más e intente nuevamente.'
  userNotFound: '{emoji} usuario no encontrado. Intente ingresar su ID en su lugar.'
  blacklisted: '{emoji} Tú o este servidor están en la lista negra de este hub llamado {hub}.'
  userBlacklisted: '{emoji} Estás en la lista negra de este centro.'
  serverBlacklisted: '{emoji} Este servidor está en la lista negra de este centro.'
  serverNotBlacklisted: '{emoji} El servidor ingresado no está en la lista negra.'
  userNotBlacklisted: '{emoji} El usuario ingresado no está en la lista negra.'
  missingPermissions: '{emoji} Me falta los siguientes permisos para realizar esta acción: **{permissions}**'
  botMissingPermissions: '{emoji} Por favor, otorgame los siguientes permisos para continuar: **{permissions}**'
  unknown: '{emoji} Ocurrió un error desconocido. Vuelva a intentarlo más tarde o contáctenos uniéndose a nuestro [servidor de soporte]({support_invite}).'
  notUsable: '{emoji} Esto ya no se puede usar.'
  cooldown: '{emoji} Estás en el tiempo de reutilización. Espere **{time}** antes de intentarlo nuevamente.'
  serverNameInappropriate: '{emoji} El nombre de tu servidor contiene palabras inapropiadas. Por favor, cámbialo antes de unirte al centro.'
  banned: |
    {emoji} Se te ha prohibido usar Interchat por violar nuestras [reglas](https://interchat.tech/guidelines).
    Si cree que es aplicable una apelación, cree un ticket en el [Servidor de soporte]({support_invite}).
config:
  setInvite:
    success: |
      ### {emoji} Conjunto de Enlace de Invitación
      - La invitación de tu servidor se utilizará cuando la gente use `/joinserver`.
      - Se mostrará en `/leaderboard server`.
    removed: '{emoji} ¡Invitacióeliminada con éxito!'
    invalid: '{emoji} La invitación es inválida. Asegúrate de haber ingresado un enlace de invitación válido. Por ejemplo. `https://discord.gg/discord`'
    notFromServer: '{emoji} Esta invitación no es de este servidor.'
badges:
  shown: '{emoji} Tus insignias ahora se mostrarán en los mensajes.'
  hidden: '{emoji} Tus insignias ahora no se mostrarán en los mensajes.'
  command:
    description: '🏅 Configura las preferencias al mostrar tus insignias'
    options:
      show:
        name: 'mostrar'
        description: 'Mostrar u ocultar tus insignias en los mensajes'
  list:
    developer: 'Desarrollador principal de InterChat'
    staff: 'Miembro del staff de InterChat'
    translator: 'Traductor del InterChat'
    voter: 'Ha votado por InterChat en las últimas 12 horas'
global:
  webhookNoLongerExists: '{emoji} El webhook para este canal ya no existe. Para continuar usando Interchat, vuelva a crear el webhook usando `/connection unpause`.'
  noReason: No se proporcionó ninguna razón.
  noDesc: Sin descripción.
  version: InterChat v{version}
  loading: '{emoji} Por favor, espere mientras proceso su solicitud...'
  reportOptionMoved: '{emoji} ¡Esta opción se ha movido! Para informar un mensaje a los moderadores del centro, use el comando actualizado `Apps > Message Info/Report`. Para informar directamente al personal de Interchat, simplemente entre al [Servidor de soporte]({support_invite}) y cree un ticket con pruebas adjuntas.'
  private: 'Privado'
  public: 'Público'
  yes: 'Sí'
  no: 'No'
  cancelled: '{emoji} Cancelado. No se han realizado cambios.'
warn:
  modal:
    title: Advertir al usuario
    reason:
      label: Razón
      placeholder: Introduzca la razón para advertir a este usuario...
  success: |
    {emoji} Se advirtió con éxito a **{name}**.

    -# Serán notificados de la advertencia más reciente la próxima vez que envíen un mensaje en el centro. Evite emitir varias advertencias a la vez.
  dm:
    title: '{emoji} Notificación de advertencia'
    description: 'Has sido advertido en el centro **{hubName}**'
  log:
    title: '{emoji} Usuario advertido'
    description: |
      {arrow} **Usuario:** {user} ({userId})
      {arrow} **Moderador:** {moderator} ({modId})
      {arrow} **Razón:** {reason}
    footer: 'Advertido por: {moderator}'
