rules:
  header: 'InterChat Rules'
  botRulesNote: 'These rules are in place to make a safe and enjoyable experience for everyone. Read and follow them carefully:'
  rules: |
    1. **No Hate Speech or Harassment**
    -# > **Includes:** Using slurs or hate speech to attack others, [and more]({guidelines_link}).
    2. **No Illegal Content**
    -# > **Includes:** Sharing links to illegal content, Encouraging violence, [and more]({guidelines_link}).
    3. **No Severe NSFW or Gore**
    -# > **Includes:** Posting gore or extreme gore in InterChat, Posting sexual content in non-NSFW hubs, [and more]({guidelines_link}).
    4. **No Spamming or Flooding**
    -# > **Includes:** Mass spamming or bot floods, [and more]({guidelines_link})
    5. **No Impersonation or Fraud**
    -# > **Includes:** Impersonating InterChat staff or hub moderators, Running cryptocurrency or NFT scams, [and more]({guidelines_link}).
    6. **No Exploitation or Abuse**
    -# > **Includes:** Grooming or predatory behavior towards minors, Sharing, Requesting, Blackmailing or threatening to  Encouraging self-harm, [and more]({guidelines_link}).
    7. **No Sharing Malicious Software**
    -# > **Includes:** Sharing malware, viruses, 'free nitro' links, harmful scripts [and more]({guidelines_link}).

    You also agree to follow [Discord's Terms of Service](https://discord.com/terms) and [Community Guidelines](https://discord.com/guidelines). Check out the [full list of rules]({guidelines_link}).
  welcome: |
    {emoji} हाय {user}! क्या आप दूसरी कम्युनिटीज़ से जुड़ने के लिए तैयार हैं?
    सर्वर के बीच बातचीत शुरू करने से पहले, आइए जल्दी से हमारी कम्युनिटी गाइडलाइन्स पर एक नज़र डाल लें।
    यही नियम InterChat को सभी के लिए एक बेहतरीन जगह बनाए रखते हैं।
  alreadyAccepted: '{emoji} आपने पहले ही नियमों को स्वीकार कर लिया है। अब आप InterChat का पूरा उपयोग कर सकते हैं।'
  continue: आगे बढ़ें
  accept: स्वीकार करें
  decline: अस्वीकार करें
  agreementNote: इन नियमों को स्वीकार करके, आप InterChat का उपयोग करते समय इन्हें पालन करने के लिए सहमति देते हैं। इन नियमों का उल्लंघन करने पर प्रतिबंध या बैन हो सकता है।
  hubAgreementNote: |
    इन नियमों को स्वीकार करके, आप इस हब में बात करते समय इन्हें पालन करने के लिए सहमति देते हैं। इन नियमों का उल्लंघन करने पर हब से हटा दिया जा सकता है।

    ⚠️ जब तक आप इन नियमों को स्वीकार नहीं करते, आप इस हब में संदेश नहीं भेज सकते। 
  accepted: |
    {emoji} You're all set! Welcome to the InterChat community.

    {emoji} You're all set! Welcome to the InterChat community.

    ### Get Started Quickly:
    - Visit `/hub browse` to find active communities
    - Join discussions that interest you or create your own through our __dashboard__
    - Use our new `/call` command to connect with single servers

    Need help? Join our [community]({support_invite}) - we're happy to help! Please consider supporting us by [donating]({donateLink}) to help InterChat grow and thrive.
  declined: |
    {emoji} You have declined the InterChat rules.
    -# ⚠️ **You won't be able to use InterChat or chat with other servers until you accept the rules.**
    -# To try again, send another message or use any InterChat command.
  hubAccepted: |
    {emoji} You have accepted the hub rules.
    You can now start chatting in this hub!
  hubDeclined: |
    {emoji} You have declined the rules for {hubName}.
    -# ⚠️ **You won't be able to send messages in this hub until you accept its rules.**
    -# To try again, send another message in this hub.
  noHubRules: इस हब ने अभी तक कोई विशेष नियम निर्धारित नहीं किए हैं। हालांकि, [general InterChat rules]({rules_link}) अभी भी लागू हैं।
  hubRules: हब नियम
  viewbotRules: "View Bot Rules"
vote:
  description: |
    Help more communities discover InterChat! Your vote on top.gg:
    - Helps others find active communities
    - Unlocks special features for you
    - Supports our independent development
  footer: "वोट हर बारह (12) घंटे में रीफ़्रेश होते हैं • InterChat का समर्थन करने के लिए धन्यवाद!"
  button:
    label: "top.gg पर वोट करें"
  perks:
    moreComingSoon: "और फायदे जल्द ही आ रहे हैं! कुछ सुझाव [सपोर्ट सर्वर]({support_invite}) में दें।"
network:
  accountTooNew: '{emoji} {user} आपका खाता InterChat का उपयोग करके संदेश भेजने के लिए बहुत नया है। कृपया बाद में प्रयास करें।'
  deleteSuccess: '{emoji} {user} द्वारा भेजा गया संदेश __**{deleted}/{total}**__ सर्वरों से हटा दिया गया है।'
  editInProgress: '{emoji} Your request has been queued. Messages will be edited shortly...'
  editInProgressError: '{emoji} This message is already being edited by another user.'
  emptyContent: '{emoji} Message content cannot be empty.'
  newMessageContent: 'New Message Content'
  editMessagePrompt: '{emoji} Please use the modal to edit your message.'
  editSuccess: '{emoji} {user} द्वारा भेजा गया संदेश __**{edited}/{total}**__ सर्वरों में संपादित कर दिया गया है।'
  onboarding:
    embed:
      title: '👋 {hubName} में आपका स्वागत है!'
      description: |
        आपने एक सक्रिय कम्युनिटी हब ढूंढ लिया है! चर्चा में शामिल होने से पहले, हमारी गाइडलाइन्स पर एक नज़र डाल लें ताकि बातचीत सभी के लिए मनोरंजक और शांतिपूर्ण बनी रहे।
      footer: InterChat नेटवर्क | संस्करण {version}
    inProgress: '{emoji} {channel} पहले से ही एक हब में शामिल होने के लिए सेटअप होने की प्रक्रिया में है। कृपया सेटअप को पूरा करने या रद्द करने के लिए प्रतीक्षा करें यदि आप इसे शुरू करने वाले थे।'
blacklist:
  success: '{emoji} **{name}** सफलतापूर्ण ब्लैकलिस्ट हो गया है!'
  removed: '{emoji} **{name}** ब्लैकलिस्ट से हटा दिया गया है!'
  modal:
    reason:
      label: कारण
      placeholder: ब्लैकलिस्ट करने का कारण
    duration:
      label: अवधि
      placeholder: 'ब्लैकलिस्ट की अवधि। जैसे: 1d, 1w, 1m, 1y। स्थायी के लिए खाली छोड़ें।'
  user:
    cannotBlacklistMod: '{emoji} You cannot blacklist a moderator. Please remove their moderator role first.'
    alreadyBlacklisted: '{emoji} यह User पहले से ही ब्लैकलिस्टेड है।'
    easterEggs:
      blacklistBot: आप मुझे ब्लैकलिस्ट नहीं कर सकते, wtf.
  server:
    alreadyBlacklisted: '{emoji} यह सर्वर पहले से ही ब्लैकलिस्टेड है।'
    unknownError: '**{server}** को ब्लैकलिस्ट करने में विफल रहा। अधिक जानकारी के लिए डेवलपर्स से पूछताछ करें।'
  list:
    user: |
      **User ID:** {id}
      **मॉडरेटर:** {moderator}
      **कारण:** {reason}
      **समाप्ति:** {expires}
    server: |
      **सर्वर ID:** {id}
      **मॉडरेटर:** {moderator}
      **कारण:** {reason}
      **समाप्ति:** {expires}
msgInfo:
  buttons:
    message: संदेश जानकारी
    server: सर्वर जानकारी
    user: User जानकारी
    report: रिपोर्ट
  report:
    notEnabled: '{emoji} इस हब के लिए रिपोर्टिंग सक्षम नहीं है।'
    success: '{emoji} रिपोर्ट सफलतापूर्वक सबमिट की गई। धन्यवाद!'
invite: |
  Thank you for choosing to invite InterChat! If you have any questions or need help, we are always here to help you in the support server!

  **[{invite_emoji} `Invite Link`]( {invite} ) [{support_emoji} `Support Server`]( {support} )**
connection:
  joinRequestsDisabled: '{emoji} Join requests are disabled for this hub.'
  notFound: '{emoji} अमान्य कनेक्शन। चैनल ID की पुष्टि करें या प्रदर्शित विकल्पों से चुनें।'
  channelNotFound: '{emoji} कनेक्टेड चैनल नहीं मिला। फिर से बात करने के लिए एक नया चैनल चुनें।'
  alreadyConnected: '{emoji} चैनल {channel} पहले से ही एक हब से कनेक्टेड है।'
  switchChannel: '{emoji} नीचे दिए गए चयन मेनू का उपयोग करके स्विच करने के लिए एक चैनल चुनें:'
  switchCalled: '{emoji} Channel switch called, use the command again to view new connection.'
  switchSuccess: '{emoji} चैनल स्विच हो गया है। आप अब **{channel}** से कनेक्ट हो गए हैं।'
  inviteRemoved: '{emoji} इस हब के लिए सर्वर आमंत्रण हटा दिया गया है।'
  setInviteError: '{emoji} Unable to create invite. Please grant me  the `Create Invite` permission for the connected channel.'
  inviteAdded: '{emoji} Invite Added. Others can now join this server by using `Apps > Message Info/Report` command and `/joinserver` command.'
  emColorInvalid: '{emoji} अमान्य रंग। कृपया सुनिश्चित करें कि आपने एक वैध हेक्स रंग कोड दर्ज किया है।'
  emColorChange: '{emoji} एंबेड रंग सफलतापूर्वक {action}'
  embed:
    title: कनेक्शन जानकारी
    fields:
      hub: हब
      channel: चैनल
      invite: आमंत्रण
      connected: Connected
      emColor: एंबेड रंग
      compact: Compact Mode
    footer: Use the dropdown menu below to manage your connection.
  selects:
    placeholder: '🛠️ इस कनेक्शन को संपादित करने के लिए एक विकल्प चुनें'
  unpaused:
    desc: |
      ### {tick_emoji} Unpaused Connection

      Unpaused connection for {channel}! Messages from the hub will start coming into the channel and you can send messages to the hub again.
    tips: |
      **💡 Tip:** Use {pause_cmd} to pause the connection or {edit_cmd} to set embed colors, invite to your server and more.
  paused:
    desc: |
      ### {clock_emoji} Paused Connection
      Paused connection for {channel}! Messages from the hub will no longer come into the channel and your messages won't be broadcasted to them.
    tips: |
      **💡 Tip:** Use {unpause_cmd} to unpause the connection or {leave_cmd} to permanently stop recieving messages.
hub:
  notFound: '{emoji} Unable to find hub. Please make sure you have entered the correct hub name.'
  notFound_mod: '{emoji} Unable to find hub. Please make sure you have entered the correct hub name & that you are the owner or a moderator of the hub.'
  notManager: '{emoji} You must be a hub manager to perform this action.'
  notModerator: '{emoji} You need to be a hub moderator to perform this action.'
  notPrivate: '{emoji} This hub is not private.'
  notOwner: '{emoji} Only the owner of this hub can perform this action.'
  alreadyJoined: '{emoji} You have already joined another hub **{hub}** from {channel}! Use `/disconnect` on it and then try again using `/connect`.'
  invalidChannel: '{emoji} Invalid channel. Only text and thread channels are supported!'
  invalidImgurUrl: '{emoji} Invalid image URL for icon or banner. Please make sure you have entered a valid Imgur image URL that is not a gallery or album.'
  join:
    success: |
      Successfully joined hub **{hub}** from {channel}! You can now chat with members from other servers from this channel.
      - Use `/connection` to explore various customizations for this connection.
      - Use `/disconnect` to stop receiving messages from this hub.
      - Use **`/connection edit`** to change channels.
  servers:
    total: 'Current connected servers: {from}-{to} / **{total}**'
    noConnections: '{emoji} No server has joined this hub yet. Use `/connect` to join this hub.'
    notConnected: "{emoji} That server isn't a part of **{hub}**."
    connectionInfo: |
      ServerID: {serverId}
      Channel: #{channelName} `({channelId})`
      Joined At: {joinedAt}
      Invite: {invite}
      Connected: {connected}
  blockwords:
    deleted: '{emoji} Anti-Swear rule successfully deleted!'
    notFound: '{emoji} Anti-Swear rule not found.'
    maxRules: '{emoji} You have reached the maximum number of anti-swear rules (2) for this hub. Please delete a rule before adding another one.'
    configure: 'Configure actions for rule: {rule}'
    actionsUpdated: '{emoji} Updated the actions to be taken by the rule. **New Actions:** {actions}'
    selectRuleToEdit: Select a rule to edit it's words/actions
    listDescription: |
      ### {emoji} Anti-Swear Rules
      This hub has {totalRules}/2 anti-swear rules setup.
    listFooter: Select a rule using the menu to view it's full details.
    ruleDescription: |
      ### {emoji} Editing Rule: {ruleName}
      {words}
    ruleFooter: 'Click the button below to edit the words or rule name!'
    actionSelectPlaceholder: 'Select the actions this rule should perform.'
    embedFields:
      noActions: '{emoji} **None!** Configure using the menu below.'
      actionsName: 'Configured Actions:'
      actionsValue: '{actions}'
    modal:
      addRule: Add Anti-Swear Rule
      editingRule: Editing Anti-Swear Rule
      ruleNameLabel: Rule Name
      wordsLabel: 'Words'
      wordsPlaceholder: 'Words seperated by comma. (Use * for wildcard). Eg. word1, *word2*, *word3, word4*'
    validating: '{emoji} Validating anti-swear rule...'
    noRules: |
      ### {emoji} Let's set up some anti-swear rules!
      Use the `Add Rule` button to create one.
  create:
    modal:
      title: Create Hub
      name:
        label: Hub Name
        placeholder: Enter a name for your hub.
      description:
        label: Description
        placeholder: Enter a description for your hub.
      icon:
        label: Icon URL
        placeholder: Enter an Imgur image URL.
      banner:
        label: Banner URL
        placeholder: Enter an Imgur image URL.
    maxHubs: '{emoji} [Vote for InterChat]({voteUrl}) to create more hubs! You have reached the maximum number of hubs ({maxHubs}) you can create.'
    invalidName: '{emoji} Invalid hub name. It must not contain `discord`, `clyde` or \`\`\` . Please choose another name.'
    nameTaken: '{emoji} This hub name is already taken. Please choose another name.'
    success: |
      ## Hub Created! It is __private__ by default.
      Use `/hub edit hub:{name}` to customize your hub. Please follow the steps below to get started:
      ### Next Steps:
      1. **Create an Invite:**
      > Use `/hub invite create` to create an invite for others to join.
      2. **Link a Channel:**
      > Use `/connect` **with the invite link previously generated** to link a channel to the hub and start chatting.
      3. **Configure Hub:** (Recommended)
      > Use `/hub config settings`, `/hub config logging` & `/hub config anti-swear` to configure the hub.
      4. **Add Moderators:**
      > Use `/hub moderator add` to add moderators to the hub.
      5. **Customize Hub:**
      > Use `/hub edit` to change the hub icon, banner, and description.
      6. **Go Public:**
      > Use `/hub visibility` to make the hub public and allow others to browse and join it without using invites. (Optional)

      If you have any questions or need help, feel free to ask in the [support server]({support_invite}). Consider [donating]({donateLink}) to support the development costs.
  delete:
    confirm: Are you sure you wish to delete **{hub}**? This action is irreversible. All connected servers, invites and message data will be removed from this hub.
    ownerOnly: '{emoji} Only the owner of this hub can delete it.'
    success: '{emoji} Hub **{hub}** has been deleted.'
    cancelled: '{emoji} Hub deletion has been cancelled.'
  browse:
    joinConfirm: |
      Are you sure you wish to join {hub} from {channel}?

      **Note:** You can always change this later using `/connection`.
    joinFooter: Want to use a different channel? Use the dropdown below.
    noHubs: '{emoji} There are no hubs listed here at the moment. Please try again later!'
    rating:
      invalid: Invalid rating. You must enter a number between 1 and 5.
      success: Rating submitted! Thank you for your feedback.
  invite:
    create:
      success: |
        ### Invite Created!

        Your invite has been successfully created. Others can now join this hub by using the `/connect` command.

        - **Join using:** `/connect invite:{inviteCode}`
        - **View invites:** `/hub invite list`
        - **Expiry:** {expiry}
        - **Uses**: ∞

        **Note:** You can revoke this invite anytime using `/hub invite revoke {inviteCode}`.
    revoke:
      invalidCode: '{emoji} Invalid invite code. Please make sure you have entered a valid invite code.'
      success: '{emoji} Invite {inviteCode} revoked.'
    list:
      title: '**Invite Codes:**'
      noInvites: '{emoji} This hub has no invites yet. Use `/hub invite create` to create one.'
      notPrivate: '{emoji} Only private hubs can have invites. Use `/hub edit` to make this hub private.'
  joined:
    noJoinedHubs: '{emoji} This server has not joined any hubs yet. Use `/hub browse` to view a list of hubs.'
    joinedHubs: This server is a part of **{total}** hub(s). Use `/disconnect` to leave a hub.
  leave:
    noHub: '{emoji} That channel is invalid or has not joined any hubs.'
    confirm: Are you sure you wish to leave **{hub}** from {channel}? No more messages will be sent to this server from this hub.
    confirmFooter: Confirm using the button below within 10 seconds.
    success: '{emoji} Left the hub from {channel}. No more messages will be sent to this server from this hub. You can rejoin using `/connect`.'
  moderator:
    noModerators: '{emoji} This hub has no moderators yet. Use `/hub moderator add` to add one.'
    add:
      success: '{emoji} **{user}** has been added as a moderator of position **{position}**.'
      alreadyModerator: '{emoji} **{user}** is already a moderator.'
    remove:
      success: '{emoji} **{user}** has been removed as a moderator.'
      notModerator: '{emoji} **{user}** is not a moderator.'
      notOwner: '{emoji} Only the owner of this hub can remove a manager.'
    update:
      success: "{emoji} **{user}**'s position has been updated to **{position}**."
      notModerator: "{emoji} **{user}** is not a moderator."
      notAllowed: "{emoji} Only hub managers can update a moderator's position."
      notOwner: "{emoji} Only the owner of this hub can update a manager's position."
  manage:
    dashboardTip: "**🛠️ NEW Dashboard:** Improved interface and more features! Try it out at [your hub's dashboard page]({url})."
    enterImgurUrl: Enter a valid Imgur image URL that is not a gallery or album.
    icon:
      changed: Hub icon successfully changed.
      modal:
        title: Edit Icon
        label: Icon URL
      selects:
        label: Edit Icon
        description: Change the icon of this hub.
    description:
      changed: Hub description successfully changed.
      modal:
        title: Edit Description
        label: Description
        placeholder: Enter a description for this hub.
      selects:
        label: Change Description
        description: Change the description of this hub.
    banner:
      changed: Hub banner successfully changed.
      removed: Hub banner successfully removed.
      modal:
        title: Edit Banner
        label: Banner URL
      selects:
        label: Edit Banner
        description: Change the banner of this hub.
    visibility:
      success: '{emoji} Hub visibility successfully changed to **{visibility}**.'
      selects:
        label: Change Visibility
        description: Make this hub public or private.
    toggleLock:
      selects:
        label: 'Lock/Unlock Hub'
        description: 'Lock or unlock the hub chats'
      confirmation: 'Hub chats are now {status}.'
      announcementTitle: 'Hub chats are now {status}.'
      announcementDescription:
        locked: 'Only moderators can send messages.'
        unlocked: 'Everyone can send messages.'
    embed:
      visibility: 'Visibility'
      connections: 'Connections'
      chatsLocked: 'Chats Locked'
      blacklists: 'Blacklists'
      total: 'Total'
      users: 'Users'
      servers: 'Servers'
      hubStats: 'Hub Stats'
      moderators: 'Moderators'
      owner: 'Owner'
    logs:
      title: Logs Configuration
      reset: '{emoji} Successfully reset the logs configuration for `{type}` logs.'
      roleSuccess: '{emoji} Logs of type `{type}` will now mention {role}!'
      roleRemoved: '{emoji} Logs of type `{type}` will no longer mention a role.'
      channelSuccess: '{emoji} Logs of type `{type}` will be sent to  {channel} from now!'
      channelSelect: '#️⃣ Select a channel to send the logs'
      roleSelect: '🏓 Select the role to mention when a log is triggered.'
      reportChannelFirst: '{emoji} Please set a log channel first.'
      config:
        title: Configure `{type}` Logs
        description: |
          {arrow} Select a log channel and/or role to be pinged from the dropdown below.
          {arrow} You can also disable logging by using the button below.
        fields:
          channel: Channel
          role: Role Mention
      reports:
        label: Reports
        description: Receive reports from users.
      modLogs:
        label: Mod Logs
        description: Log Moderation actions. (eg. blacklist, message deletes, etc.)
      joinLeaves:
        label: Join/Leave
        description: Log when a server joins or leaves this hub.
      appeals:
        label: Appeals
        description: Recieve appeals from blacklisted users/servers.
      networkAlerts:
        label: Network Alerts
        description: Recieve alerts about automatically blocked messages.
  transfer:
    invalidUser: '{emoji} The specified user was not found.'
    selfTransfer: '{emoji} You cannot transfer ownership to yourself.'
    botUser: '{emoji} You cannot transfer ownership to a bot.'
    confirm: 'Are you sure you want to transfer ownership of **{hub}** to {newOwner}? You will be demoted to manager role.'
    cancelled: '{emoji} Hub transfer has been cancelled.'
    error: '{emoji} An error occurred while transferring hub ownership.'
    success: '{emoji} Successfully transferred ownership of **{hub}** to {newOwner}. You have been added as a manager.'
    timeout: '{emoji} Hub transfer has timed out.'
  rules:
    noRules: "{emoji} This hub has no rules configured yet. Let's add some!"
    list: "### {emoji} Hub Rules\n{rules}"
    maxRulesReached: "{emoji} Maximum number of rules ({max}) reached."
    ruleExists: "{emoji} This rule already exists."
    selectedRule: "Selected Rule {number}"
    modal:
      add:
        title: Add Hub Rule
        label: Rule Text
        placeholder: Enter the rule text (max 1000 characters)
      edit:
        title: Edit Hub Rule
        label: Rule Text
        placeholder: Enter the new rule text (max 1000 characters)
    select:
      placeholder: Select a rule to edit or remove
      option:
        label: Rule {number}
    buttons:
      add: Add Rule
      edit: Edit Rule
      delete: Delete Rule
      back: Back
    success:
      add: '{emoji} Rule added successfully!'
      edit: '{emoji} Rule updated successfully!'
      delete: '{emoji} Rule deleted successfully!'
    view:
      title: 'Rule {number}'
      select: Select an action for this rule
  welcome:
    set: '{emoji} Welcome message updated successfully!'
    removed: '{emoji} Welcome message removed.'
    voterOnly: '{emoji} Custom welcome messages are a voter-only perk! Vote to unlock this feature.'
    placeholder: |
      Welcome {user} from {serverName} to {hubName}! 🎉
      Members: {memberCount}, Hub: {totalConnections}!
report:
  modal:
    title: Report Details
    other:
      label: Report Details
      placeholder: A detailed description of the report.
    bug:
      input1:
        label: Bug Details
        placeholder: Eg. Frequent interaction failures for /help command...
      input2:
        label: Detailed Description (Optional)
        placeholder: Steps you took. Eg. 1. Run /help 2. Wait for 5 seconds...
  reasons:
    spam: Spam or excessive messages
    advertising: Unwanted advertising or self-promotion
    nsfw: NSFW or inappropriate content
    harassment: Harassment or bullying
    hate_speech: Hate speech or discrimination
    scam: Scam, fraud, or phishing attempt
    illegal: Illegal content or activities
    personal_info: Sharing personal/private information
    impersonation: Impersonating others
    breaks_hub_rules: Violates hub rules
    trolling: Trolling or intentional disruption
    misinformation: False or misleading information
    gore_violence: Gore or extreme violence
    raid_organizing: Organizing raids or attacks
    underage: Underage user or content
  dropdown:
    placeholder: Select a reason for your report
  submitted: '{emoji} Report submitted successfully. Join the {support_command} to get more details. Thank you!'
  bug:
    title: Bug Report
    affected: Affected Components
    description: Please choose what component of the bot you are facing issues with.
language:
  set: Language set! I will now respond to you in **{lang}**.
errors:
  messageNotSentOrExpired: '{emoji} This message was not sent in a hub, has expired, or you lack permissions to perform this action.'
  notYourAction: "{emoji} Sorry, you can't perform this action. Please run the command yourself."
  notMessageAuthor: '{emoji} You are not the author of this message.'
  commandError: |
    {emoji} An error occurred while executing this command. It has been logged to our system. If this issue persists, please join our [support server]({support_invite}) and report the error ID!

    **Error ID:**
    ```{errorId}```
  mustVote: Please [vote](https://top.gg/bot/769921109209907241/vote) for InterChat to use this command, your support is very much appreciated!
  inviteLinks: '{emoji} You may not send invite links to this hub. Set an invite in `/connection` instead! Hub mods can configure this using `/hub edit settings`'
  invalidLangCode: '{emoji} Invalid language code. Please make sure you have entered a correct [language code](https://cloud.google.com/translate/docs/languages).'
  unknownServer: '{emoji} Unknown server. Please make sure you have entered the correct **Server ID**.'
  unknownNetworkMessage: '{emoji} Unknown Message. If it has been sent in the past minute, please wait few more seconds and try again.'
  userNotFound: '{emoji} User not found. Try inputting their ID instead.'
  blacklisted: '{emoji} You or this server is blacklisted from this hub called {hub}.'
  userBlacklisted: '{emoji} You are blacklisted from this hub.'
  serverBlacklisted: '{emoji} This server is blacklisted from this hub.'
  serverNotBlacklisted: '{emoji} The inputted server is not blacklisted.'
  userNotBlacklisted: '{emoji} The inputted user is not blacklisted.'
  missingPermissions: '{emoji} You are missing the following permissions to perform this action: **{permissions}**'
  botMissingPermissions: '{emoji} Please grant me the following permissions to continue: **{permissions}**'
  unknown: '{emoji} An unknown error occurred. Please try again later or contact us by joining our [support server]({support_invite}).'
  notUsable: '{emoji} This is no longer usable.'
  cooldown: '{emoji} You are on cooldown. Please wait until **{time}** before attempting again.'
  serverNameInappropriate: '{emoji} Your server name contains inappropriate words. Please change it before joining the hub.'
  banned: |
    {emoji} You have been banned from using InterChat for violating our [guidelines](https://interchat.tech/guidelines).
    If you think an appeal is applicable create a ticket in the [support server]( {support_invite} ).
config:
  setInvite:
    success: |
      ### {emoji} Invite Link Set
      - Your server's invite will be used when people use `/joinserver`.
      - It will be displayed in `/leaderboard server`.
    removed: '{emoji} Invite removed successfully!'
    invalid: '{emoji} Invalid invite. Please make sure you have entered a valid invite link. Eg. `https://discord.gg/discord`'
    notFromServer: '{emoji} This invite is not from this server.'
badges:
  shown: '{emoji} Your badges will now be shown in messages.'
  hidden: '{emoji} Your badges will now be hidden in messages.'
  command:
    description: '🏅 Configure your badge display preferences'
    options:
      show:
        name: 'show'
        description: 'Whether to show or hide your badges in messages'
  list:
    developer: 'Core developer of InterChat'
    staff: 'InterChat staff member'
    translator: 'Translator of InterChat'
    voter: 'Voted for InterChat in the last 12 hours'
global:
  webhookNoLongerExists: '{emoji} The webhook for this channel no longer exists. To continue using InterChat, please re-create the webhook by using `/connection unpause`.'
  noReason: No reason provided.
  noDesc: No Description.
  version: InterChat v{version}
  loading: '{emoji} Please wait while I process your request...'
  reportOptionMoved: '{emoji} This option has moved! To report a message to hub moderators, use the updated `Apps > Message Info/Report` command. For direct reporting to InterChat staff, just hop into the [support server]({support_invite}) and create a ticket with proof.'
  private: 'Private'
  public: 'Public'
  yes: 'Yes'
  no: 'No'
  cancelled: '{emoji} Cancelled. No changes were made.'
warn:
  modal:
    title: Warn User
    reason:
      label: Reason
      placeholder: Enter the reason for warning this user...
  success: |
    {emoji} Successfully warned **{name}**.

    -# They will be notified of the most recent warning the next time they send a message in the hub. Avoid issuing multiple warnings at once.
  dm:
    title: '{emoji} Warning Notification'
    description: 'You have been warned in hub **{hubName}**'
  log:
    title: '{emoji} User Warned'
    description: |
      {arrow} **User:** {user} ({userId})
      {arrow} **Moderator:** {moderator} ({modId})
      {arrow} **Reason:** {reason}
    footer: 'Warned by: {moderator}'
