rules:
  header: 'Regras do InterChat'
  botRulesNote: 'Estas regras existem para garantir uma experiência segura e agradável para todos. Leia e siga-as com atenção:'
  rules: |
    1. **Sem Discurso de Ódio ou Assédio**
    -# > **Inclui:** Usar insultos ou discurso de ódio para atacar outros, [e mais]({guidelines_link}).
    2. **Sem Conteúdo Ilegal**
    -# > **Inclui:** Compartilhar links para conteúdo ilegal, Incentivar violência, [e mais]({guidelines_link}).
    3. **Sem NSFW Grave ou Gore**
    -# > **Inclui:** Postar gore ou gore extremo no InterChat, Postar conteúdo sexual em hubs não-NSFW, [e mais]({guidelines_link}).
    4. **Sem Spamming ou Inundação**
    -# > **Inclui:** Spam em massa ou inundações de bots, [e mais]({guidelines_link})
    5. **Sem Falsificação ou Fraude**
    -# > **Inclui:** Se passar por membros da equipe do InterChat ou moderadores de hubs, Executar golpes com criptomoedas ou NFTs, [e mais]({guidelines_link}).
    6. **Sem Exploração ou Abuso**
    -# > **Inclui:** Comportamento de aliciamento ou predatório com menores, Compartilhar, Solicitar, Chantagear ou ameaçar, Incentivar automutilação, [e mais]({guidelines_link}).
    7. **Sem Compartilhar Software Malicioso**
    -# > **Inclui:** Compartilhar malware, vírus, links de 'nitro grátis', scripts prejudiciais [e mais]({guidelines_link}).

    Você também concorda em seguir os [Termos de Serviço do Discord](https://discord.com/terms) e as [Diretrizes da Comunidade](https://discord.com/guidelines). Confira a [lista completa de regras]({guidelines_link}).
  welcome: |
    {emoji} Olá {user}! Pronto para se conectar com outras comunidades?
    Antes de começar a bater papo entre servidores, vamos revisar rapidamente nossas diretrizes da comunidade. Elas ajudam a manter o InterChat um ótimo lugar para todos.
  alreadyAccepted: '{emoji} Você já aceitou as regras. Agora você pode usar o InterChat em toda sua danelização.'
  continue: Continuar
  accept: Aceitar
  decline: Declínio
  agreementNote: Ao aceitar essas regras, você concorda em segui-las enquanto usa o InterChat. Quebrar essas regras pode resultar em restrições ou banimentos.
  hubAgreementNote: |
    Ao aceitar essas regras, você concorda em segui-las enquanto estiver conversando neste hub. Quebrar essas regras pode resultar na remoção do hub.

    ⚠️ **Você não pode enviar mensagens neste hub até aceitar estas
  accepted: |
    {emoji} Você está pronto! Bem-vindo à comunidade InterChat.

    Dicas rápidas para começar:
    - Use `/hub browse` para encontrar comunidades ativas
    - Participe das discussões que te interessam
    - Use nosso novo comando `/call` para se conectar com servidores individuais

    Precisa de ajuda? Entre na nossa [comunidade]({support_invite}) - estamos felizes em ajudar!

    Gostou do que estamos construindo? Considere [nos apoiar]({donateLink}) para ajudar a manter o InterChat funcionando.
  declined: |
    {emoji} Você recusou as regras do InterChat.
    -# ⚠️ **Você não poderá usar o InterChat ou bater papo com outros servidores até aceitar as regras.**
    -# Para tentar novamente, envie outra mensagem ou use qualquer Inte.
  hubAccepted: |
    {emoji} Você aceitou as regras do hub.
    Agora você pode começar a bater papo neste hub!
  hubDeclined: |
    {emoji} Você recusou as regras para {hubName}.
    -# ⚠️ **Você não poderá enviar mensagens neste hub até aceitar suas regras.**
    -# Para tentar novamente, envie outra mensagem neste hub.
  noHubRules: Este hub ainda não definiu nenhuma regra específica. No entanto, as [regras gerais do InterChat]({rules_link}) ainda se aplicam.
  hubRules: Regras do Hub
  viewbotRules: "View Bot Rules"
vote:
  description: |
    Ajude mais comunidades a descobrir o InterChat! Seu voto no top.gg:
    - Ajuda outros a encontrar comunidades ativas
    - Desbloqueia recursos especiais para você
    - Apoia nosso desenvolvimento independente
  footer: "Os votos são atualizados a cada 12 horas • Obrigado por apoiar o InterChat!"
  button:
    label: "Vote no top.gg"
  perks:
    moreComingSoon: "Mais vantagens em breve! Sugira algumas no [servidor de suporte]({support_invite})."
network:
  accountTooNew: '{emoji} {user} Sua conta é muito nova para enviar mensagens usando o InterChat. Tente novamente mais tarde.'
  deleteSuccess: '{emoji} A mensagem de {user} foi excluída de __**{deleted}/{total}**__ servidores.'
  editInProgress: '{emoji} Your request has been queued. Messages will be edited shortly...'
  editInProgressError: '{emoji} This message is already being edited by another user.'
  emptyContent: '{emoji} Message content cannot be empty.'
  newMessageContent: 'New Message Content'
  editMessagePrompt: '{emoji} Please use the modal to edit your message.'
  editSuccess: '{emoji} A mensagem de {user} foi editada em __**{edited}/{total}**__ servidores.'
  onboarding:
    embed:
      title: 'Bem-vindo ao {hubName}!'
      description: |
        Você encontrou um hub comunitário ativo! Antes de entrar na discussão, dê uma olhada rápida em nossas diretrizes para manter as conversas envolventes e amigáveis para todos.
      footer: Rede InterChat | Versão {version}
    inProgress: '{emoji} {channel} já está em processo de configuração para se juntar a um hub. Aguarde a conclusão da configuração ou cancele-a se você foi quem a iniciou.'
blacklist:
  success: '{emoji} **{name}** foi colocado na lista negra com sucesso!'
  removed: '{emoji} **{name}** foi removido da lista negra!'
  modal:
    reason:
      label: Causa
      placeholder: Motivo da lista negra
    duration:
      label: Duração
      placeholder: 'Duração da lista negra. Ex.: 1d, 1s, 1m, 1a. Deixe em branco para permanente.'
  user:
    cannotBlacklistMod: '{emoji} Você não pode colocar um moderador na lista negra. Por favor, remova a função de moderador dele primeiro.'
    alreadyBlacklisted: '{emoji} Este usuário já está na lista negra.'
    easterEggs:
      blacklistBot: Você não pode me colocar na lista negra, wtf.
  server:
    alreadyBlacklisted: '{emoji}{emoji} Este servidor já está na lista negra.'
    unknownError: Falha ao colocar **{server}** na lista negra. Entre em contato com os desenvolvedores para obter mais informações.
  list:
    user: |
      **UserID:** {id} 
      **Moderador:** {moderator} 
      **Motivo:** {reason} 
      **Expira:** {expires}
    server: |
      **ServerId:** {id}
      **Moderador:** {moderator}
      **Motivo:** {reason}
      **Expira:** {expires}
msgInfo:
  buttons:
    message: Informações da mensagem
    server: Informações do servidor
    user: Informações do usuário
    report: Reportar
  report:
    notEnabled: '{emoji} Os relatórios não estão habilitados para este hub.'
    success: '{emoji} Relatório enviado com sucesso. Obrigado!'
invite: |
  Obrigado por escolher convidar o InterChat! Se você tiver alguma dúvida ou precisar de ajuda, estamos sempre aqui para ajudar no servidor de suporte!

  **[{invite_emoji} `Link de convite`]( {invite} ) [{support_emoji}
connection:
  joinRequestsDisabled: '{emoji} As solicitações de adesão estão desabilitadas para este hub.'
  notFound: '{emoji} Conexão inválida. Verifique o ID do canal ou selecione entre as opções exibidas.'
  channelNotFound: '{emoji} Não é possível encontrar o canal conectado. Para falar novamente, escolha um novo canal.'
  alreadyConnected: '{emoji} O canal {channel} já está conectado a um hub.'
  switchChannel: '{emoji} O canal {channel} já está conectado a um hub:'
  switchCalled: '{emoji} Troca de canal chamada, use o comando novamente para visualizar a nova conexão.'
  switchSuccess: '{emoji} Canal trocado. Agora você está conectado de **{channel}**.'
  inviteRemoved: '{emoji} Convite do servidor removido para este hub.'
  setInviteError: '{emoji} Não é possível criar convite. Por favor, conceda-me a permissão `Criar convite` para o canal conectado.'
  inviteAdded: '{emoji} Invite Added. Others can now join this server by using `Apps > Message Info/Report` command and `/joinrequest` command.'
  emColorInvalid: '{emoji} Cor inválida. Certifique-se de ter inserido um código de cor hexadecimal válido.'
  emColorChange: '{emoji} Incorporar cor com sucesso {action}'
  embed:
    title: Detalhes da conexão
    fields:
      hub: Hub
      channel: Canal
      invite: Convite
      connected: Conectado
      emColor: Incorporar cor
      compact: Modo compacto
    footer: Use o menu suspenso abaixo para gerenciar sua conexão.
  selects:
    placeholder: '🛠️ Selecione uma opção para editar esta conexão'
  unpaused:
    desc: |
      ### {tick_emoji} Conexão não pausada

      Conexão não pausada para {channel}! Mensagens do hub começarão a chegar ao canal e você poderá enviar mensagens para o hub novamente.
    tips: |
      **💡 Tip:** Use {pause_cmd} to pause the connection or {edit_cmd} to set embed colors, toggle profanity filter, and more.
  paused:
    desc: |
      ### {clock_emoji} Conexão pausada Conexão pausada para {channel}! Mensagens do hub não chegarão mais ao canal e suas mensagens não serão transmitidas a eles.
    tips: |
      **💡 Dica:** Use {unpause_cmd} para retomar a conexão ou {leave_cmd} para parar permanentemente de receber mensagens.
hub:
  notFound: '{emoji} Não foi possível encontrar o hub. Certifique-se de ter inserido o nome correto do hub.'
  notFound_mod: '{emoji} Não foi possível encontrar o hub. Certifique-se de ter inserido o nome correto do hub e de que você é o proprietário ou moderador do hub.'
  notManager: '{emoji} Você deve ser um gerente de hub para executar esta ação.'
  notModerator: '{emoji} Você deve ser um gerente de hub para executar esta ação.'
  notPrivate: '{emoji} Este hub não é privado.'
  notOwner: '{emoji} Somente o proprietário deste hub pode executar esta ação.'
  alreadyJoined: '{emoji} Você já entrou no **{hub}** do {channel}!'
  invalidChannel: '{emoji} Canal inválido. Somente canais de texto e thread são suportados!'
  invalidImgurUrl: '{emoji} URL de imagem inválida para ícone ou banner. Certifique -se de que você inseriu um URL de imagem imgur válido que não é uma galeria ou álbum.'
  join:
    success: |
      . Entrou com sucesso no hub **{hub}** de {channel}! Agora você pode bater papo com membros de outros servidores deste canal. - Use `/connection` para explorar várias personalizações para esta conexão. -
      - Use `/disconnect` para parar de receber mensagens deste hub.
      - Use **`/connection edit`** para mudar de canal.
  servers:
    total: 'Servidores conectados atualmente: {from}-{to} / **{total}**'
    noConnections: '{emoji} Nenhum servidor se juntou a este hub ainda. Use `/connect` para se juntar a este hub.'
    notConnected: "{emoji} Esse servidor não faz parte do **{hub}**."
    connectionInfo: |
      ServerID: {serverId}
      Canal: #{channelName} `({channelId})`
      Ingressou em: {joinedAt}
      Convidar: {invite}
      Conectado: {connected}
  blockwords:
    deleted: '{emoji} Regra anti-palavrões excluída com sucesso!'
    notFound: '{emoji} Regra anti-palavrões não encontrada.'
    maxRules: '{emoji} Você atingiu o número máximo de regras anti-palavrões (2) para este hub. Exclua uma regra antes de adicionar outra.'
    configure: 'Configurar ações para a regra: {rule}'
    actionsUpdated: '{emoji} Atualizadas as ações a serem tomadas pela regra. **Novas ações:** {actions}'
    selectRuleToEdit: Selecione uma regra para editar suas palavras/ações
    listDescription: |
      ### {emoji} Regras anti-palavrões
      Este hub tem {totalRules}/2 regras anti-palavrões configuradas.
    listFooter: Selecione uma regra usando o menu para visualizar todos os detalhes.
    ruleDescription: |
      ### {emoji} Regra de edição: {ruleName}
      {words}
    ruleFooter: 'Clique no botão abaixo para editar as palavras ou o nome da regra!'
    actionSelectPlaceholder: 'Selecione as ações que esta regra deve executar.'
    embedFields:
      noActions: '{emoji} **Nenhum!** Configure usando o menu abaixo.'
      actionsName: 'Ações configuradas:'
      actionsValue: '{actions}'
    modal:
      addRule: Adicionar regra anti-palavrões
      editingRule: Editando a regra anti-palavrões
      ruleNameLabel: Nome da regra
      wordsLabel: 'Palavras'
      wordsPlaceholder: 'Palavras separadas por vírgula (* = curinga). Ex: palavra1, *palavra2*, *palavra3, palavra4*'
    validating: '{emoji} Validando regra anti-palavrões...'
    noRules: |
      ### {emoji} Vamos configurar algumas regras anti-palavrões!
      Use o botão `Add Rule` para criar uma.
  create:
    modal:
      title: Criar Hub
      name:
        label: Nome do Hub
        placeholder: Insira um nome para seu hub.
      description:
        label: Descrição
        placeholder: Insira uma descrição para seu hub.
      icon:
        label: URL do ícone
        placeholder: Insira uma URL de imagem do Imgur.
      banner:
        label: URL do banner
        placeholder: Insira uma URL de imagem do Imgur.
    maxHubs: 'emoji} Você atingiu o número máximo de hubs ({maxHubs}) que pode criar. Exclua um hub antes de criar outro. [Vote no InterChat]({voteUrl}) para criar mais hubs.'
    invalidName: '{emoji} Nome de hub inválido. Não deve conter `discord`, `clyde` ou \`\`\` . Escolha outro nome.'
    nameTaken: '{emoji} Este nome de hub já foi usado. Por favor, escolha outro nome.'
    success: |
      ### Your __private__ hub, **{name}**, has been successfully created.
      To join, create an invite using `/hub invite create` and share the generated code. Then join using `/connect`.

      - **Edit hub:** `/hub edit`
      - **Generate invite:** `/hub invite create`
      - **Make public:** `/hub visibility`
      - **Join hub:** `/connect`
      - **Edit hub:** `/hub edit`
      - **Set report channel:** `/hub logging set_channe;`
      - **Add moderators:** `/hub moderator add`

      If you have any questions or need help, feel free to ask in the [support server]({support_invite}). Consider [donating]({donateLink}) to support the development costs.
  delete:
    confirm: Tem certeza de que deseja excluir **{hub}**? Esta ação é irreversível. Todos os servidores conectados, convites e dados de mensagens serão removidos deste hub.
    ownerOnly: '{emoji} Somente o proprietário deste hub pode excluí-lo.'
    success: '{emoji} O hub **{hub}** foi excluído.'
    cancelled: '{emoji} A exclusão do Hub foi cancelada.'
  browse:
    joinConfirm: |
      Tem certeza de que deseja ingressar no {hub} a partir do {channel}?

      **Observação:** você sempre pode alterar isso mais tarde usando `/connection`.
    joinFooter: Quer usar um canal diferente? Use o menu suspenso abaixo.
    noHubs: '{emoji} Não há hubs listados aqui no momento. Tente novamente mais tarde!'
    rating:
      invalid: Classificação inválida. Você deve digitar um número entre 1 e 5.
      success: Avaliação enviada! Obrigado pelo seu feedback.
  invite:
    create:
      success: |
        ### Convite criado!

        Seu convite foi criado com sucesso. Outros agora podem participar deste hub usando o comando `/connect`.

        - **Participar usando:** `/connect invite:{inviteCode}`
        - **Visualizar convites:** `/`hub lista de convites`
        - **Expiração:** {expiração}
        - **Usos**: ∞.
    revoke:
      invalidCode: '{emoji} Código de convite inválido. Certifique-se de ter inserido um código de convite válido.'
      success: '{emoji} Convite {inviteCode} revogado.'
    list:
      title: '**Códigos de convite:**'
      noInvites: '**Códigos de convite:**'
      notPrivate: '{emoji} Somente hubs privados podem ter convites. Use `/hub edit` para tornar este hub privado.'
  joined:
    noJoinedHubs: '{emoji} Este servidor ainda não se juntou a nenhum hub. Use `/hub browse` para visualizar uma lista de hubs.'
    joinedHubs: Este servidor faz parte de **{total}** hub(s). Use `/disconnect` para sair de um hub.
  leave:
    noHub: '{emoji} Esse canal é inválido ou não se juntou a nenhum hub.'
    confirm: Tem certeza de que deseja sair de **{hub}** de {channel}? Nenhuma outra mensagem será enviada para este servidor deste hub.
    confirmFooter: Confirme usando o botão abaixo em até 10 segundos.
    success: '{emoji} Saiu do hub de {channel}. Nenhuma outra mensagem será enviada para este servidor deste hub. Você pode entrar novamente usando `/connect`.'
  moderator:
    noModerators: '{emoji} Este hub ainda não tem moderadores. Use `/hub moderator add` para adicionar um.'
    add:
      success: '{emoji} **{user}** foi adicionado como moderador da posição **{position}**.'
      alreadyModerator: '{emoji} **{user}** já é um moderador.'
    remove:
      success: '{emoji} **{user}** foi removido como moderador.'
      notModerator: '{emoji} **{user}** não é um moderador.'
      notOwner: '{emoji} Somente o proprietário deste hub pode remover um gerente.'
    update:
      success: "{emoji} A posição de **{user}** foi atualizada para **{position}**."
      notModerator: "{emoji} **{user}** não é um moderador."
      notAllowed: "{emoji} Somente os gerentes do hub podem atualizar a posição de um moderador."
      notOwner: "{emoji} Somente o proprietário deste hub pode atualizar a posição de um gerente."
  manage:
    dashboardTip: "**🛠️ NEW Dashboard:** Improved interface and more features! Try it out at [your hub's dashboard page]({url})."
    enterImgurUrl: Insira uma URL de imagem Imgur válida que não seja uma galeria ou álbum.
    icon:
      changed: Ícone do Hub alterado com sucesso.
      modal:
        title: Ícone de edição
        label: URL do ícone
      selects:
        label: Ícone de edição
        description: Alterar o ícone deste hub.
    description:
      changed: '{emoji} Somente os gerentes do hub podem atualizar a posição de um moderador.'
      modal:
        title: Editar Descrição
        label: Descrição
        placeholder: Insira uma descrição para este hub.
      selects:
        label: Alterar descrição
        description: Alterar a descrição deste hub.
    banner:
      changed: O banner do hub foi alterado com sucesso.
      removed: O banner do Hub foi removido com sucesso.
      modal:
        title: Editar Banner
        label: URL do banner
      selects:
        label: Editar Banner
        description: Alterar o banner deste hub.
    visibility:
      success: '{emoji} A visibilidade do Hub foi alterada com sucesso para **{visibility}**.'
      selects:
        label: Alterar Visibilidade
        description: Torne este hub público ou privado.
    toggleLock:
      selects:
        label: 'Bloquear/Desbloquear Hub'
        description: 'Bloquear ou desbloquear os chats do hub'
      confirmation: 'Os chats do Hub agora são {status}.'
      announcementTitle: 'Os chats do Hub agora são {status}.'
      announcementDescription:
        locked: 'Somente moderadores podem enviar mensagens.'
        unlocked: 'Todos podem enviar mensagens.'
    embed:
      visibility: 'Visibilidade'
      connections: 'Conexões'
      chatsLocked: 'Bate-papos bloqueados'
      blacklists: 'Listas negras'
      total: 'Total'
      users: 'Usuários'
      servers: 'Servidores'
      hubStats: 'Estatísticas do Hub'
      moderators: 'Moderadores'
      owner: 'Dono'
    logs:
      title: Configuração de Logs
      reset: '{emoji} A configuração de logs para logs `{type}` foi redefinida com sucesso.'
      roleSuccess: '{emoji} Os logs do tipo `{type}` agora mencionarão {role}!'
      roleRemoved: '{emoji} Logs do tipo `{type}` não mencionarão mais uma função.'
      channelSuccess: '{emoji} Logs of type `{type}` will be sent to  {channel} from now!'
      channelSelect: '#️⃣ Selecione um canal para enviar os logs'
      roleSelect: 'Selecione a função a ser mencionada quando um log for acionado.'
      reportChannelFirst: '{emoji} Por favor, defina um canal de log primeiro.'
      config:
        title: Configurar logs `{type}`
        description: |
          {arrow} Selecione um canal de log e/ou função para ser pingado no menu suspenso abaixo.
          {arrow} Você também pode desabilitar o log usando o botão abaixo.
        fields:
          channel: Canal
          role: Menção de função
      reports:
        label: Relatórios
        description: Receba relatórios dos usuários.
      modLogs:
        label: Registros de Mods
        description: Ações de moderação de log. (por exemplo, lista negra, exclusões de mensagens, etc.)
      joinLeaves:
        label: Entrar/Sair
        description: Registre quando um servidor entra ou sai deste hub.
      appeals:
        label: Apelações
        description: Receba apelações de usuários/servidores na lista negra.
      networkAlerts:
        label: Alertas de rede
        description: Receba alertas sobre mensagens bloqueadas automaticamente.
  transfer:
    invalidUser: '.{emoji} O usuário especificado não foi encontrado.'
    selfTransfer: '{emoji} Você não pode transferir a propriedade para si mesmo.'
    botUser: '{emoji} Você não pode transferir a propriedade para um bot.'
    confirm: 'Tem certeza de que deseja transferir a propriedade de **{hub}** para {newOwner}? Você será rebaixado para a função de gerente.'
    cancelled: '{emoji} A transferência do Hub foi cancelada.'
    error: '{emoji} Ocorreu um erro ao transferir a propriedade do hub.'
    success: '{emoji} Propriedade de **{hub}** transferida com sucesso para {newOwner}. Você foi adicionado como gerente.'
    timeout: '{emoji} Hub transfer has timed out.'
  rules:
    noRules: "{emoji} This hub has no rules configured yet. Let's add some!"
    list: "### {emoji} Regras do Hub\n{regras"
    maxRulesReached: "{emoji} Número máximo de regras ({max}) atingido."
    ruleExists: "e{moji} Esta regra já existe."
    selectedRule: "Regra selecionada {número}"
    modal:
      add:
        title: Adicionar Regra de Hub
        label: Texto da regra
        placeholder: Insira o texto da regra (máx. 1000 caracteres
      edit:
        title: Editar Regra do Hub
        label: Texto da regra
        placeholder: Insira o novo texto da regra (máx. 1000 caracteres)
    select:
      placeholder: Selecione uma regra para editar ou remover
      option:
        label: Regra {number}
    buttons:
      add: Adicionar regra
      edit: Editar regra
      delete: Excluir regra
      back: Voltar
    success:
      add: '{emoji} Regra adicionada com sucesso!'
      edit: '{emoji} Regra atualizada com sucesso!'
      delete: '{emoji} Regra excluída com sucesso!'
    view:
      title: 'Regra {número}'
      select: Selecione uma ação para esta regra
  welcome:
    set: '{emoji} Mensagem de boas-vindas atualizada com sucesso{emoji} Mensagem de boas-vindas atualizada com sucesso!'
    removed: '{emoji} Mensagem de boas-vindas removida.'
    voterOnly: '{emoji} Mensagens de boas-vindas personalizadas são um privilégio exclusivo para eleitores! Vote para desbloquear este recurso.'
    placeholder: |
      Bem-vindo {user} de {serverName} ao {hubName}! 🎉
      Membros: {memberCount}, Hub: {totalConnections}!
report:
  modal:
    title: Detalhes do relatório
    other:
      label: Detalhes do relatório
      placeholder: Uma descrição detalhada do relatório.
    bug:
      input1:
        label: Detalhes do bug
        placeholder: 'Por exemplo: Falhas frequentes de interação para o comando /help...'
      input2:
        label: Descrição detalhada (opcional)
        placeholder: 'Passos que você deu. Por exemplo: 1. Execute /help 2. Aguarde 5 segundos...'
  reasons:
    spam: Spam ou mensagens excessivas
    advertising: Publicidade indesejada ou autopromoção
    nsfw: Conteúdo NSFW ou inapropriado
    harassment: Assédio ou intimidação
    hate_speech: Discurso de ódio ou discriminação
    scam: Golpe, fraude ou tentativa de phishing
    illegal: Conteúdo ou atividades ilegais
    personal_info: Compartilhamento de informações pessoais/privadas
    impersonation: Representando outros
    breaks_hub_rules: Viola as regras do hub
    trolling: Trolling ou interrupção intencional
    misinformation: Informações falsas ou enganosas
    gore_violence: Sangue ou violência extrema
    raid_organizing: Organizar ataques ou incursões
    underage: Usuário ou conteúdo menor de idade
  dropdown:
    placeholder: Selecione um motivo para o seu relatório
  submitted: '{emoji} Relatório enviado com sucesso. Junte-se ao {support_command} para obter mais detalhes. Obrigado!'
  bug:
    title: Relatório de erro
    affected: Componentes afetados
    description: . Selecione com qual componente do bot você está tendo problemas.
language:
  set: Idioma definido! Agora responderei a você em **{lang}**. 🇧🇷🇵🇹.
errors:
  messageNotSentOrExpired: '{emoji} Esta mensagem não foi enviada em um hub, expirou ou você não tem permissão para executar esta ação.'
  notYourAction: "{emoji} Desculpe, você não pode executar esta ação. Por favor, execute o comando você mesmo."
  notMessageAuthor: '{emoji} Você não é o autor desta mensagem.'
  commandError: |
    {emoji} Ocorreu um erro ao executar este comando. Ele foi registrado em nosso sistema. Se o problema persistir, junte-se ao nosso [servidor de suporte]({support_invite}) e informe o ID do erro!

    **Erro ID:**
    ```{errorId}```
  mustVote: Por favor, [vote](https://top.gg/bot/769921109209907241/vote) para que o InterChat use este comando, seu apoio é muito apreciado!
  inviteLinks: '{emoji} Você não pode enviar links de convite para este hub. Defina um convite em `/connection` em vez disso! Os moderadores do hub podem configurar isso usando `/hub edit settings`'
  invalidLangCode: '{emoji} Código de idioma inválido. Verifique se você inseriu um [código de idioma](https://cloud.google.com/translate/docs/languages) correto.'
  unknownServer: '{emoji} Servidor desconhecido. Certifique-se de ter inserido o **ID do servidor** correto.'
  unknownNetworkMessage: '{emoji} Mensagem desconhecida. Se foi enviada no último minuto, aguarde mais alguns segundos e tente novamente.'
  userNotFound: '.{emoji} Usuário não encontrado. Tente inserir o ID dele.'
  blacklisted: '.{emoji} Usuário não encontrado. Tente inserir o ID dele.'
  userBlacklisted: '.'
  serverBlacklisted: '{emoji} Este servidor está na lista negra deste hub.'
  serverNotBlacklisted: '{emoji} O servidor inserido não está na lista negra.'
  userNotBlacklisted: '{emoji} O usuário inserido não está na lista negra.'
  missingPermissions: '{emoji} Você não tem as seguintes permissões para executar esta ação: **{permissions}**'
  botMissingPermissions: '{emoji} Por favor, conceda-me as seguintes permissões para continuar: **{permissions}**'
  unknown: '{emoji} Ocorreu um erro desconhecido. Tente novamente mais tarde ou entre em contato conosco entrando em nosso [servidor de suporte]({support_invite}).'
  notUsable: '{emoji} Isso não pode mais ser usado.'
  cooldown: '{emoji} Você está em cooldown. Aguarde até **{time}** antes de tentar novamente.'
  serverNameInappropriate: '{emoji} O nome do seu servidor contém palavras inapropriadas. Por favor, altere-o antes de entrar no hub.'
  banned: |
    {emoji} Você foi banido do uso do InterChat por violar nossas [diretrizes](https://interchat.tech/guidelines).
    Se você acha que um recurso é aplicável, crie um tíquete no [servidor de suporte]{support_invite} ).
config:
  setInvite:
    success: |
      ### {emoji} Conjunto de links de convite
      - O convite do seu servidor será usado quando as pessoas usarem `/joinserver`.
      - Ele será exibido em `/leaderboard server`.
    removed: '{emoji} Invite removed successfully!{emoji} Convite removido com sucesso!'
    invalid: '{emoji} Convite inválido. Certifique-se de ter inserido um link de convite válido. Por exemplo, `https://discord.gg/discord`'
    notFromServer: '{emoji} Este convite não é deste servidor'
badges:
  shown: '{emoji} Seus emblemas agora serão exibidos nas mensagens'
  hidden: '{emoji} Seus emblemas agora ficarão ocultos nas mensagens.'
  command:
    description: ' Configure suas preferências de exibição de crachá'
    options:
      show:
        name: 'mostrar'
        description: 'Se deseja mostrar ou ocultar seus emblemas nas mensagens'
  list:
    developer: 'Desenvolvedor principal do InterChat'
    staff: 'Membro da equipe InterChat'
    translator: 'Tradutor do InterChat'
    voter: 'Votou no InterChat nas últimas 12 horas'
global:
  webhookNoLongerExists: '{emoji} O webhook para este canal não existe mais. Para continuar usando o InterChat, recrie o webhook usando `/connection unpause`.'
  noReason: Nenhuma razão fornecida.
  noDesc: Sem descrição.
  version: InterChat v{version}
  loading: '{emoji} Aguarde enquanto processo sua solicitação...'
  reportOptionMoved: '{emoji} Esta opção foi movida! Para reportar uma mensagem aos moderadores do hub, use o comando atualizado `Apps > Message Info/Report`. Para reportar diretamente à equipe do InterChat, basta entrar no [servidor de suporte]((support_invite}) e crie um tíquete com comprovante.'
  private: 'Privado'
  public: 'Publico'
  yes: 'Sim'
  no: 'Não'
  cancelled: '{emoji} Cancelled. No changes were made.'
warn:
  modal:
    title: Avisar usuário
    reason:
      label: Razão
      placeholder: Insira o motivo do aviso a este usuário...
  success: |
    {emoji} **{name}** foi avisado com sucesso.

    -# Eles serão notificados do aviso mais recente na próxima vez que enviarem uma mensagem no hub. Evite emitir vários avisos de uma vez.
  dm:
    title: '{emoji} Notificação de Aviso'
    description: 'Você foi avisado no hub **{hubName}**'
  log:
    title: '{emoji} Usuário avisado'
    description: |
      {arrow} **Usuário:** {user} ({userId})
      {arrow} **Moderador:** {moderator} ({modId})
      {arrow} **Motivo:** {reason}
    footer: 'Avisado por: {moderador}'
