rules:
  rules: |
    ### {emoji} Rules
    These rules are in place to make a safe and enjoyable experience for everyone. Read and follow them carefully:

    1. **No Hate Speech or Harassment**
    -# > **Includes:** Using slurs or hate speech to attack others, [and more]({guidelines_link}).
    2. **No Illegal Content**
    -# > **Includes:** Sharing links to illegal content, Encouraging violence, [and more]({guidelines_link}).
    3. **No Severe NSFW or Gore**
    -# > **Includes:** Posting gore or extreme gore in InterChat, Posting sexual content in non-NSFW hubs, [and more]({guidelines_link}).
    4. **No Spamming or Flooding**
    -# > **Includes:** Mass spamming or bot floods, [and more]({guidelines_link})
    5. **No Impersonation or Fraud**
    -# > **Includes:** Impersonating InterChat staff or hub moderators, Running cryptocurrency or NFT scams, [and more]({guidelines_link}).
    6. **No Exploitation or Abuse**
    -# > **Includes:** Grooming or predatory behavior towards minors, Sharing, Requesting, Blackmailing or threatening to  Encouraging self-harm, [and more]({guidelines_link}).
    7. **No Sharing Malicious Software**
    -# > **Includes:** Sharing malware, viruses, 'free nitro' links, harmful scripts [and more]({guidelines_link}).

    You also agree to follow [Discord's Terms of Service](https://discord.com/terms) and [Community Guidelines](https://discord.com/guidelines). Check out the [full list of rules]({guidelines_link}).
  welcome: |
    {emoji} Hi {user}! Ready to connect with other communities?
    Before you start chatting across servers, let's quickly review our community guidelines. These help keep InterChat a great place for everyone.
  alreadyAccepted: '{emoji} You have already accepted the rules. You can now use InterChat to its full extent.'
  continue: Continue
  accept: Accept
  decline: Decline
  agreementNote: By accepting these rules, you agree to follow them while using InterChat. Breaking these rules may result in restrictions or bans.
  hubAgreementNote: |
    By accepting these rules, you agree to follow them while chatting in this hub. Breaking these rules may result in removal from the hub.

    ⚠️ **You cannot send messages in this hub until you accept these rules.**
  accepted: |
    {emoji} You're all set! Welcome to the InterChat community.

    Quick tips to get started:
    - Use `/hub browse` to find active communities
    - Join discussions that interest you
    - Use our new `/call` command to connect with single servers

    Need help? Join our [community]({support_invite}) - we're happy to help!

    Love what we're building? Consider [supporting us]({donateLink}) to help keep InterChat running.
  declined: |
    {emoji} You have declined the InterChat rules.
    -# ⚠️ **You won't be able to use InterChat or chat with other servers until you accept the rules.**
    -# To try again, send another message or use any InterChat command.
  hubAccepted: |
    {emoji} You have accepted the hub rules.
    You can now start chatting in this hub!
  hubDeclined: |
    {emoji} You have declined the rules for {hubName}.
    -# ⚠️ **You won't be able to send messages in this hub until you accept its rules.**
    -# To try again, send another message in this hub.
  noHubRules: This hub has not set any specific rules yet. However, the [general InterChat rules]({rules_link}) still apply.
  hubRules: Hub Rules
vote:
  description: |
    Help more communities discover InterChat! Your vote on top.gg:
    • Helps others find active communities
    • Unlocks special features for you
    • Supports our independent development
  footer: "Votes refresh every 12 hours • Thanks for supporting InterChat!"
  button:
    label: "Vote on top.gg"
  perks:
    moreComingSoon: "More perks coming soon! Suggest some in the [support server]({support_invite})."
network:
  accountTooNew: '{emoji} {user} Your account is too new to send messages using InterChat. Please try again later.'
  deleteSuccess: '{emoji} Message by {user} has been deleted from __**{deleted}/{total}**__ servers.'
  editSuccess: '{emoji} Message by {user} has been edited in __**{edited}/{total}**__ servers.'
  onboarding:
    embed:
      title: '👋 Welcome to {hubName}!'
      description: |
        You've found an active community hub! Before joining the discussion, take a quick look at our guidelines to keep conversations engaging and friendly for everyone.
      footer: InterChat Network | Version {version}
    inProgress: '{emoji} {channel} is already in the process of being setup to join a hub. Please wait for the setup to complete or cancel it if you were the one who initiated it.'
blacklist:
  success: '{emoji} **{name}** has been successfully blacklisted!'
  removed: '{emoji} **{name}** has been removed from the blacklist!'
  modal:
    reason:
      label: Reason
      placeholder: Reason for blacklisting
    duration:
      label: Duration
      placeholder: 'Duration of blacklist. Eg: 1d, 1w, 1m, 1y. Leave blank for permanent.'
  user:
    cannotBlacklistMod: '{emoji} You cannot blacklist a moderator. Please remove their moderator role first.'
    alreadyBlacklisted: '{emoji} This user is already blacklisted.'
    easterEggs:
      blacklistBot: You can't blacklist me wtf.
  server:
    alreadyBlacklisted: '{emoji} This server is already blacklisted.'
    unknownError: Failed to blacklist **{server}**. Enquire with the developers for more information.
  list:
    user: |
      **UserID:** {id}
      **Moderator:** {moderator}
      **Reason:** {reason}
      **Expires:** {expires}
    server: |
      **ServerId:** {id}
      **Moderator:** {moderator}
      **Reason:** {reason}
      **Expires:** {expires}
msgInfo:
  buttons:
    message: Message Info
    server: Server Info
    user: User Info
    report: Report
  report:
    notEnabled: '{emoji} Reporting is not enabled for this hub.'
    success: '{emoji} Report submitted successfully. Thank you!'
invite: |
  Thank you for choosing to invite InterChat! If you have any questions or need help, we are always here to help you in the support server!

  **[{invite_emoji} `Invite Link`]( {invite} ) [{support_emoji} `Support Server`]( {support} )**
connection:
  joinRequestsDisabled: '{emoji} Join requests are disabled for this hub.'
  notFound: '{emoji} Invalid connection. Verify the channel ID or select from displayed options.'
  channelNotFound: '{emoji} Unable to find connected channel. To talk again choose a new channel.'
  alreadyConnected: '{emoji} Channel {channel} is already connected to a hub.'
  switchChannel: '{emoji} Select a channel to switch to using the select menu below:'
  switchCalled: '{emoji} Channel switch called, use the command again to view new connection.'
  switchSuccess: '{emoji} Channel switched. You are now connected from **{channel}**.'
  inviteRemoved: '{emoji} Server Invite removed for this hub.'
  setInviteError: '{emoji} Unable to create invite. Please grant me  the `Create Invite` permission for the connected channel.'
  inviteAdded: '{emoji} Invite Added. Others can now join this server by using `Apps > Message Info/Report` command and `/joinserver` command.'
  emColorInvalid: '{emoji} Invalid color. Please make sure you have entered a valid hex color code.'
  emColorChange: '{emoji} Embed color successfully {action}'
  embed:
    title: Connection Details
    fields:
      hub: Hub
      channel: Channel
      invite: Invite
      connected: Connected
      emColor: Embed Color
      compact: Compact Mode
    footer: Use the dropdown menu below to manage your connection.
  selects:
    placeholder: '🛠️ Select an option to edit this connection'
  unpaused:
    desc: |
      ### {tick_emoji} Unpaused Connection

      Unpaused connection for {channel}! Messages from the hub will start coming into the channel and you can send messages to the hub again.
    tips: |
      **💡 Tip:** Use {pause_cmd} to pause the connection or {edit_cmd} to set embed colors, invite to your server and more.
  paused:
    desc: |
      ### {clock_emoji} Paused Connection
      Paused connection for {channel}! Messages from the hub will no longer come into the channel and your messages won't be broadcasted to them.
    tips: |
      **💡 Tip:** Use {unpause_cmd} to unpause the connection or {leave_cmd} to permanently stop recieving messages.
hub:
  notFound: '{emoji} Unable to find hub. Please make sure you have entered the correct hub name.'
  notFound_mod: '{emoji} Unable to find hub. Please make sure you have entered the correct hub name & that you are the owner or a moderator of the hub.'
  notManager: '{emoji} You must be a hub manager to perform this action.'
  notModerator: '{emoji} You need to be a hub moderator to perform this action.'
  notPrivate: '{emoji} This hub is not private.'
  notOwner: '{emoji} Only the owner of this hub can perform this action.'
  alreadyJoined: '{emoji} You have already joined another hub **{hub}** from {channel}! Use `/disconnect` on it and then try again using `/connect`.'
  invalidChannel: '{emoji} Invalid channel. Only text and thread channels are supported!'
  invalidImgurUrl: '{emoji} Invalid image URL for icon or banner. Please make sure you have entered a valid Imgur image URL that is not a gallery or album.'
  join:
    success: |
      Successfully joined hub **{hub}** from {channel}! You can now chat with members from other servers from this channel.
      - Use `/connection` to explore various customizations for this connection.
      - Use `/disconnect` to stop receiving messages from this hub.
      - Use **`/connection edit`** to change channels.
  servers:
    total: 'Current connected servers: {from}-{to} / **{total}**'
    noConnections: '{emoji} No server has joined this hub yet. Use `/connect` to join this hub.'
    notConnected: "{emoji} That server isn't a part of **{hub}**."
    connectionInfo: |
      ServerID: {serverId}
      Channel: #{channelName} `({channelId})`
      Joined At: {joinedAt}
      Invite: {invite}
      Connected: {connected}
  blockwords:
    deleted: '{emoji} Anti-Swear rule successfully deleted!'
    notFound: '{emoji} Anti-Swear rule not found.'
    maxRules: '{emoji} You have reached the maximum number of anti-swear rules (2) for this hub. Please delete a rule before adding another one.'
    configure: 'Configure actions for rule: {rule}'
    actionsUpdated: '{emoji} Updated the actions to be taken by the rule. **New Actions:** {actions}'
    selectRuleToEdit: Select a rule to edit it's words/actions
    listDescription: |
      ### {emoji} Anti-Swear Rules
      This hub has {totalRules}/2 anti-swear rules setup.
    listFooter: Select a rule using the menu to view it's full details.
    ruleDescription: |
      ### {emoji} Editing Rule: {ruleName}
      {words}
    ruleFooter: 'Click the button below to edit the words or rule name!'
    actionSelectPlaceholder: 'Select the actions this rule should perform.'
    embedFields:
      noActions: '{emoji} **None!** Configure using the menu below.'
      actionsName: 'Configured Actions:'
      actionsValue: '{actions}'
    modal:
      addRule: Add Anti-Swear Rule
      editingRule: Editing Anti-Swear Rule
      ruleNameLabel: Rule Name
      wordsLabel: 'Words'
      wordsPlaceholder: 'Words seperated by comma. (Use * for wildcard). Eg. word1, *word2*, *word3, word4*'
    validating: '{emoji} Validating anti-swear rule...'
    noRules: |
      ### {emoji} Let's set up some anti-swear rules!
      Use the `Add Rule` button to create one.
  create:
    modal:
      title: Create Hub
      name:
        label: Hub Name
        placeholder: Enter a name for your hub.
      description:
        label: Description
        placeholder: Enter a description for your hub.
      icon:
        label: Icon URL
        placeholder: Enter an Imgur image URL.
      banner:
        label: Banner URL
        placeholder: Enter an Imgur image URL.
    maxHubs: '{emoji} You have reached the maximum number of hubs ({maxHubs}) you can create. Please delete a hub before creating another one. [Vote for InterChat]({voteUrl}) to create more hubs.'
    invalidName: '{emoji} Invalid hub name. It must not contain `discord`, `clyde` or \`\`\` . Please choose another name.'
    nameTaken: '{emoji} This hub name is already taken. Please choose another name.'
    success: |
      ## Hub Created! It is __private__ by default.
      Use `/hub edit hub:{name}` to customize your hub. Please follow the steps below to get started:
      ### Next Steps:
      1. **Create an Invite:**
      > Use `/hub invite create` to create an invite for others to join.
      2. **Link a Channel:**
      > Use `/connect` **with the invite link previously generated** to link a channel to the hub and start chatting.
      3. **Configure Hub:** (Recommended)
      > Use `/hub config settings`, `/hub config logging` & `/hub config anti-swear` to configure the hub.
      4. **Add Moderators:**
      > Use `/hub moderator add` to add moderators to the hub.
      5. **Customize Hub:**
      > Use `/hub edit` to change the hub icon, banner, and description.
      6. **Go Public:**
      > Use `/hub visibility` to make the hub public and allow others to browse and join it without using invites. (Optional)

      If you have any questions or need help, feel free to ask in the [support server]({support_invite}). Consider [donating]({donateLink}) to support the development costs.
  delete:
    confirm: Are you sure you wish to delete **{hub}**? This action is irreversible. All connected servers, invites and message data will be removed from this hub.
    ownerOnly: '{emoji} Only the owner of this hub can delete it.'
    success: '{emoji} Hub **{hub}** has been deleted.'
    cancelled: '{emoji} Hub deletion has been cancelled.'
  browse:
    joinConfirm: |
      Are you sure you wish to join {hub} from {channel}?

      **Note:** You can always change this later using `/connection`.
    joinFooter: Want to use a different channel? Use the dropdown below.
    noHubs: '{emoji} There are no hubs listed here at the moment. Please try again later!'
    rating:
      invalid: Invalid rating. You must enter a number between 1 and 5.
      success: Rating submitted! Thank you for your feedback.
  invite:
    create:
      success: |
        ### Invite Created!

        Your invite has been successfully created. Others can now join this hub by using the `/connect` command.

        - **Join using:** `/connect invite:{inviteCode}`
        - **View invites:** `/hub invite list`
        - **Expiry:** {expiry}
        - **Uses**: ∞

        **Note:** You can revoke this invite anytime using `/hub invite revoke {inviteCode}`.
    revoke:
      invalidCode: '{emoji} Invalid invite code. Please make sure you have entered a valid invite code.'
      success: '{emoji} Invite {inviteCode} revoked.'
    list:
      title: '**Invite Codes:**'
      noInvites: '{emoji} This hub has no invites yet. Use `/hub invite create` to create one.'
      notPrivate: '{emoji} Only private hubs can have invites. Use `/hub edit` to make this hub private.'
  joined:
    noJoinedHubs: '{emoji} This server has not joined any hubs yet. Use `/hub browse` to view a list of hubs.'
    joinedHubs: This server is a part of **{total}** hub(s). Use `/disconnect` to leave a hub.
  leave:
    noHub: '{emoji} That channel is invalid or has not joined any hubs.'
    confirm: Are you sure you wish to leave **{hub}** from {channel}? No more messages will be sent to this server from this hub.
    confirmFooter: Confirm using the button below within 10 seconds.
    success: '{emoji} Left the hub from {channel}. No more messages will be sent to this server from this hub. You can rejoin using `/connect`.'
  moderator:
    noModerators: '{emoji} This hub has no moderators yet. Use `/hub moderator add` to add one.'
    add:
      success: '{emoji} **{user}** has been added as a moderator of position **{position}**.'
      alreadyModerator: '{emoji} **{user}** is already a moderator.'
    remove:
      success: '{emoji} **{user}** has been removed as a moderator.'
      notModerator: '{emoji} **{user}** is not a moderator.'
      notOwner: '{emoji} Only the owner of this hub can remove a manager.'
    update:
      success: "{emoji} **{user}**'s position has been updated to **{position}**."
      notModerator: "{emoji} **{user}** is not a moderator."
      notAllowed: "{emoji} Only hub managers can update a moderator's position."
      notOwner: "{emoji} Only the owner of this hub can update a manager's position."
  manage:
    enterImgurUrl: Enter a valid Imgur image URL that is not a gallery or album.
    icon:
      changed: Hub icon successfully changed.
      modal:
        title: Edit Icon
        label: Icon URL
      selects:
        label: Edit Icon
        description: Change the icon of this hub.
    description:
      changed: Hub description successfully changed.
      modal:
        title: Edit Description
        label: Description
        placeholder: Enter a description for this hub.
      selects:
        label: Change Description
        description: Change the description of this hub.
    banner:
      changed: Hub banner successfully changed.
      removed: Hub banner successfully removed.
      modal:
        title: Edit Banner
        label: Banner URL
      selects:
        label: Edit Banner
        description: Change the banner of this hub.
    visibility:
      success: '{emoji} Hub visibility successfully changed to **{visibility}**.'
      selects:
        label: Change Visibility
        description: Make this hub public or private.
    toggleLock:
      selects:
        label: 'Lock/Unlock Hub'
        description: 'Lock or unlock the hub chats'
      confirmation: 'Hub chats are now {status}.'
      announcementTitle: 'Hub chats are now {status}.'
      announcementDescription:
        locked: 'Only moderators can send messages.'
        unlocked: 'Everyone can send messages.'
    embed:
      visibility: 'Visibility'
      connections: 'Connections'
      chatsLocked: 'Chats Locked'
      blacklists: 'Blacklists'
      total: 'Total'
      users: 'Users'
      servers: 'Servers'
      hubStats: 'Hub Stats'
      moderators: 'Moderators'
      owner: 'Owner'
    logs:
      title: Logs Configuration
      reset: '{emoji} Successfully reset the logs configuration for `{type}` logs.'
      roleSuccess: '{emoji} Logs of type `{type}` will now mention {role}!'
      roleRemoved: '{emoji} Logs of type `{type}` will no longer mention a role.'
      channelSuccess: '{emoji} Logs of type `{type}` will be sent to  {channel} from now!'
      channelSelect: '#️⃣ Select a channel to send the logs'
      roleSelect: '🏓 Select the role to mention when a log is triggered.'
      reportChannelFirst: '{emoji} Please set a log channel first.'
      config:
        title: Configure `{type}` Logs
        description: |
          {arrow} Select a log channel and/or role to be pinged from the dropdown below.
          {arrow} You can also disable logging by using the button below.
        fields:
          channel: Channel
          role: Role Mention
      reports:
        label: Reports
        description: Receive reports from users.
      modLogs:
        label: Mod Logs
        description: Log Moderation actions. (eg. blacklist, message deletes, etc.)
      joinLeaves:
        label: Join/Leave
        description: Log when a server joins or leaves this hub.
      appeals:
        label: Appeals
        description: Recieve appeals from blacklisted users/servers.
      networkAlerts:
        label: Network Alerts
        description: Recieve alerts about automatically blocked messages.
  transfer:
    invalidUser: '{emoji} The specified user was not found.'
    selfTransfer: '{emoji} You cannot transfer ownership to yourself.'
    botUser: '{emoji} You cannot transfer ownership to a bot.'
    confirm: 'Are you sure you want to transfer ownership of **{hub}** to {newOwner}? You will be demoted to manager role.'
    cancelled: '{emoji} Hub transfer has been cancelled.'
    error: '{emoji} An error occurred while transferring hub ownership.'
    success: '{emoji} Successfully transferred ownership of **{hub}** to {newOwner}. You have been added as a manager.'
    timeout: '{emoji} Hub transfer has timed out.'
  rules:
    noRules: "{emoji} This hub has no rules configured yet. Let's add some!"
    list: "### {emoji} Hub Rules\n{rules}"
    maxRulesReached: "{emoji} Maximum number of rules ({max}) reached."
    ruleExists: "{emoji} This rule already exists."
    selectedRule: "Selected Rule {number}"
    modal:
      add:
        title: Add Hub Rule
        label: Rule Text
        placeholder: Enter the rule text (max 1000 characters)
      edit:
        title: Edit Hub Rule
        label: Rule Text
        placeholder: Enter the new rule text (max 1000 characters)
    select:
      placeholder: Select a rule to edit or remove
      option:
        label: Rule {number}
    buttons:
      add: Add Rule
      edit: Edit Rule
      delete: Delete Rule
      back: Back
    success:
      add: '{emoji} Rule added successfully!'
      edit: '{emoji} Rule updated successfully!'
      delete: '{emoji} Rule deleted successfully!'
    view:
      title: 'Rule {number}'
      select: Select an action for this rule
  welcome:
    set: '{emoji} Welcome message updated successfully!'
    removed: '{emoji} Welcome message removed.'
    voterOnly: '{emoji} Custom welcome messages are a voter-only perk! Vote to unlock this feature.'
    placeholder: |
      Welcome {user} from {serverName} to {hubName}! 🎉
      Members: {memberCount}, Hub: {totalConnections}!
report:
  modal:
    title: Report Details
    other:
      label: Report Details
      placeholder: A detailed description of the report.
    bug:
      input1:
        label: Bug Details
        placeholder: Eg. Frequent interaction failures for /help command...
      input2:
        label: Detailed Description (Optional)
        placeholder: Steps you took. Eg. 1. Run /help 2. Wait for 5 seconds...
  submitted: '{emoji} Report submitted successfully. Join the {support_command} to get more details. Thank you!'
  bug:
    title: Bug Report
    affected: Affected Components
    description: Please choose what component of the bot you are facing issues with.
language:
  set: Language set! I will now respond to you in **{lang}**.
errors:
  messageNotSentOrExpired: '{emoji} This message was not sent in a hub, has expired, or you lack permissions to perform this action.'
  notYourAction: "{emoji} Sorry, you can't perform this action. Please run the command yourself."
  notMessageAuthor: '{emoji} You are not the author of this message.'
  commandError: |
    {emoji} An error occurred while executing this command. It has been logged to our system. If this issue persists, please join our [support server]({support_invite}) and report the error ID!

    **Error ID:**
    ```{errorId}```
  mustVote: Please [vote](https://top.gg/bot/769921109209907241/vote) for InterChat to use this command, your support is very much appreciated!
  inviteLinks: '{emoji} You may not send invite links to this hub. Set an invite in `/connection` instead! Hub mods can configure this using `/hub edit settings`'
  invalidLangCode: '{emoji} Invalid language code. Please make sure you have entered a correct [language code](https://cloud.google.com/translate/docs/languages).'
  unknownServer: '{emoji} Unknown server. Please make sure you have entered the correct **Server ID**.'
  unknownNetworkMessage: '{emoji} Unknown Message. If it has been sent in the past minute, please wait few more seconds and try again.'
  userNotFound: '{emoji} User not found. Try inputting their ID instead.'
  blacklisted: '{emoji} You or this server is blacklisted from this hub called {hub}.'
  userBlacklisted: '{emoji} You are blacklisted from this hub.'
  serverBlacklisted: '{emoji} This server is blacklisted from this hub.'
  serverNotBlacklisted: '{emoji} The inputted server is not blacklisted.'
  userNotBlacklisted: '{emoji} The inputted user is not blacklisted.'
  missingPermissions: '{emoji} You are missing the following permissions to perform this action: **{permissions}**'
  botMissingPermissions: '{emoji} Please grant me the following permissions to continue: **{permissions}**'
  unknown: '{emoji} An unknown error occurred. Please try again later or contact us by joining our [support server]({support_invite}).'
  notUsable: '{emoji} This is no longer usable.'
  cooldown: '{emoji} You are on cooldown. Please wait until **{time}** before attempting again.'
  serverNameInappropriate: '{emoji} Your server name contains inappropriate words. Please change it before joining the hub.'
  banned: |
    {emoji} You have been banned from using InterChat for violating our [guidelines](https://interchat.tech/guidelines).
    If you think an appeal is applicable create a ticket in the [support server]( {support_invite} ).
config:
  setInvite:
    success: |
      ### {emoji} Invite Link Set
      - Your server's invite will be used when people use `/joinserver`.
      - It will be displayed in `/leaderboard server`.
    removed: '{emoji} Invite removed successfully!'
    invalid: '{emoji} Invalid invite. Please make sure you have entered a valid invite link. Eg. `https://discord.gg/discord`'
    notFromServer: '{emoji} This invite is not from this server.'
badges:
  shown: '{emoji} Your badges will now be shown in messages.'
  hidden: '{emoji} Your badges will now be hidden in messages.'
  command:
    description: '🏅 Configure your badge display preferences'
    options:
      show:
        name: 'show'
        description: 'Whether to show or hide your badges in messages'
  list:
    developer: 'Core developer of InterChat'
    staff: 'InterChat staff member'
    translator: 'Translator of InterChat'
    voter: 'Voted for InterChat in the last 12 hours'
global:
  webhookNoLongerExists: '{emoji} The webhook for this channel no longer exists. To continue using InterChat, please re-create the webhook by using `/connection unpause`.'
  noReason: No reason provided.
  noDesc: No Description.
  version: InterChat v{version}
  loading: '{emoji} Please wait while I process your request...'
  reportOptionMoved: '{emoji} This option has moved! To report a message to hub moderators, use the updated `Apps > Message Info/Report` command. For direct reporting to InterChat staff, just hop into the [support server]({support_invite}) and create a ticket with proof.'
  private: 'Private'
  public: 'Public'
  yes: 'Yes'
  no: 'No'
  cancelled: '{emoji} Cancelled. No changes were made.'
warn:
  modal:
    title: Warn User
    reason:
      label: Reason
      placeholder: Enter the reason for warning this user...
  success: |
    {emoji} Successfully warned **{name}**.

    -# They will be notified of the most recent warning the next time they send a message in the hub. Avoid issuing multiple warnings at once.
  dm:
    title: '{emoji} Warning Notification'
    description: 'You have been warned in hub **{hubName}**'
  log:
    title: '{emoji} User Warned'
    description: |
      {arrow} **User:** {user} ({userId})
      {arrow} **Moderator:** {moderator} ({modId})
      {arrow} **Reason:** {reason}
    footer: 'Warned by: {moderator}'
