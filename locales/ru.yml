rules:
  header: 'InterChat Rules'
  botRulesNote: 'These rules are in place to make a safe and enjoyable experience for everyone. Read and follow them carefully:'
  rules: |
    1. **No Hate Speech or Harassment**
    -# > **Includes:** Using slurs or hate speech to attack others, [and more]({guidelines_link}).
    2. **No Illegal Content**
    -# > **Includes:** Sharing links to illegal content, Encouraging violence, [and more]({guidelines_link}).
    3. **No Severe NSFW or Gore**
    -# > **Includes:** Posting gore or extreme gore in InterChat, Posting sexual content in non-NSFW hubs, [and more]({guidelines_link}).
    4. **No Spamming or Flooding**
    -# > **Includes:** Mass spamming or bot floods, [and more]({guidelines_link})
    5. **No Impersonation or Fraud**
    -# > **Includes:** Impersonating InterChat staff or hub moderators, Running cryptocurrency or NFT scams, [and more]({guidelines_link}).
    6. **No Exploitation or Abuse**
    -# > **Includes:** Grooming or predatory behavior towards minors, Sharing, Requesting, Blackmailing or threatening to  Encouraging self-harm, [and more]({guidelines_link}).
    7. **No Sharing Malicious Software**
    -# > **Includes:** Sharing malware, viruses, 'free nitro' links, harmful scripts [and more]({guidelines_link}).

    You also agree to follow [Discord's Terms of Service](https://discord.com/terms) and [Community Guidelines](https://discord.com/guidelines). Check out the [full list of rules]({guidelines_link}).
  welcome: |
    {emoji} Hi {user}! Ready to connect with other communities?
    Before you start chatting across servers, let's quickly review our community guidelines. These help keep InterChat a great place for everyone.
  alreadyAccepted: '{emoji} Вы уже приняли правила. Теперь вы можете в полной мере использовать InterChat.'
  continue: Продолжить
  accept: Принять
  decline: Отклонить
  agreementNote: Принимая эти правила, вы соглашаетесь следовать им при использовании InterChat. Нарушение этих правил может привести к ограничениям или блокировке.
  hubAgreementNote: |
    By accepting these rules, you agree to follow them while chatting in this hub. Breaking these rules may result in removal from the hub.

    ⚠️ **You cannot send messages in this hub until you accept these rules.**
  accepted: |
    {emoji} You're all set! Welcome to the InterChat community.

    {emoji} You're all set! Welcome to the InterChat community.

    ### Get Started Quickly:
    - Visit `/hub browse` to find active communities
    - Join discussions that interest you or create your own through our __dashboard__
    - Use our new `/call` command to connect with single servers

    Need help? Join our [community]({support_invite}) - we're happy to help! Please consider supporting us by [donating]({donateLink}) to help InterChat grow and thrive.
  declined: |
    {emoji} You have declined the InterChat rules.
    -# ⚠️ **You won't be able to use InterChat or chat with other servers until you accept the rules.**
    -# To try again, send another message or use any InterChat command.
  hubAccepted: |
    {emoji} You have accepted the hub rules.
    You can now start chatting in this hub!
  hubDeclined: |
    {emoji} You have declined the rules for {hubName}.
    -# ⚠️ **You won't be able to send messages in this hub until you accept its rules.**
    -# To try again, send another message in this hub.
  noHubRules: This hub has not set any specific rules yet. However, the [general InterChat rules]({rules_link}) still apply.
  hubRules: Hub Rules
  viewbotRules: "View Bot Rules"
vote:
  description: |
    Help more communities discover InterChat! Your vote on top.gg:
    - Helps others find active communities
    - Unlocks special features for you
    - Supports our independent development
  footer: "Votes refresh every 12 hours • Thanks for supporting InterChat!"
  button:
    label: "Vote on top.gg"
  perks:
    moreComingSoon: "More perks coming soon! Suggest some in the [support server]({support_invite})."
network:
  accountTooNew: '{emoji} {user} Ваш аккаунт слишком молодой для отправки сообщений в InterChat. Пожалуйста, попробуйте позже.'
  deleteSuccess: '{emoji} Сообщение {user} было удалено из __**{deleted}/{total}**__ серверов.'
  editInProgress: '{emoji} Your request has been queued. Messages will be edited shortly...'
  editInProgressError: '{emoji} This message is already being edited by another user.'
  emptyContent: '{emoji} Message content cannot be empty.'
  newMessageContent: 'New Message Content'
  editMessagePrompt: '{emoji} Please use the modal to edit your message.'
  editSuccess: '{emoji} Сообщение {user} было отредактировано на __**{edited}/{total}**__ серверах.'
  onboarding:
    embed:
      title: '👋 Welcome to {hubName}!'
      description: |
        You've found an active community hub! Before joining the discussion, take a quick look at our guidelines to keep conversations engaging and friendly for everyone.
      footer: Сеть InterChat | Версия {version}
    inProgress: '{emoji} {channel} уже в процессе подключения к хабу. Пожалуйста, подождите пока процесс завершится или отмените его если вы его начали.'
blacklist:
  success: '{emoji} **{name}** был успешно добавлен в чёрный список!'
  removed: '{emoji} **{name}** был успешно убран из чёрного списка!'
  modal:
    reason:
      label: Причина
      placeholder: Причина для добавления в чёрный список
    duration:
      label: Длительность
      placeholder: 'Длительность нахождения в черном списке. Например: 1d, 1w, 1m, 1y. Оставьте поле пустым для бессрочного.'
  user:
    cannotBlacklistMod: '{emoji} Вы не можете поместить модератора в чёрный список. Сначала снимите с них роль.'
    alreadyBlacklisted: '{emoji} Этот пользователь уже в чёрном списке.'
    easterEggs:
      blacklistBot: Вы не можете поместить меня в черный список чзх.
  server:
    alreadyBlacklisted: '{emoji} Этот сервер уже заблокирован.'
    unknownError: Не удалось поместить **{server}** в чёрный список. Свяжитесь с разработчиками для получения дополнительной информации.
  list:
    user: |
      **ID Пользователя:** {id}
      **Модератор:** {moderator}
      **Причина:** {reason}
      **Истекает:** {expires}
    server: |
      **ID Сервера:** {id}
      **Модератор** {moderator}
      **Причина:** {reason}
      **Истекает:**{expires}
msgInfo:
  buttons:
    message: Информация о сообщении
    server: Информация о сервере
    user: Информация о пользователе
    report: Пожаловаться
  report:
    notEnabled: '{emoji} Система жалоб не включена в этом хабе.'
    success: '{emoji} Жалоба успешно отправлена. Спасибо!'
invite: |
  Спасибо за то что выбрали InterChat! Если у вас есть какие-либо вопросы или нужна помощь, мы всегда готовы помочь вам на сервере поддержки!

  **[{invite_emoji} `Ссылка Приглашения`] ( {invite} ) [{support_emoji} `Сервер Поддержки`] ( {support} )
connection:
  joinRequestsDisabled: '{emoji} Запросы на вступление были выключены в этом хабе.'
  notFound: '{emoji} Неверное соединение. Проверьте ID канала или выберите из представленных опций.'
  channelNotFound: '{emoji} Не могу найти соединённый канал. Чтобы снова общаться выберите новый канал.'
  alreadyConnected: '{emoji} Канал {channel} уже присоединён к этому хабу.'
  switchChannel: '{emoji} Выберите канал для переключения с помощью меню выбора ниже:'
  switchCalled: '{emoji} Совершено переключение канала, используйте команду снова, чтобы просмотреть новое соединение.'
  switchSuccess: '{emoji} Канал переключён. Теперь вы присоединены из **{channel}**ю'
  inviteRemoved: '{emoji} Приглашение сервера было убрано из этого хаба.'
  setInviteError: '{emoji} Невозможно создать приглашение. Предоставьте мне разрешение на создание приглашений в выбранном канале.'
  inviteAdded: '{emoji} Приглашение добавлено. Другие люди теперь могут присоединиться к вашему серверу используя команду `/joinserver`.'
  emColorInvalid: '{emoji} Некорректный цвет. Пожалуйста, убедитесь что вы ввели правильный HEX-Код цвета.'
  emColorChange: '{emoji} Цвет вставки {action}'
  embed:
    title: Детали соединения
    fields:
      hub: Хаб
      channel: Канал
      invite: Приглашение
      connected: Соединено
      emColor: Цвет вставки
      compact: Компактный режим
    footer: Используйте меню ниже, чтобы управлять вашим соединением.
  selects:
    placeholder: '🛠️ Выберите опцию для этого соединения'
  unpaused:
    desc: |
      ### {tick_emoji} Соединение снято с паузы

      {channel} Был снят с паузы! Сообщения из хаба будут приходить сюда и вы сможете отправлять в него сообщения снова.
    tips: |
      **💡 Примечание:**Используйте {pause_cmd} чтобы остановить соединение или {edit_cmd} чтобы изменить цвета сообщений, пригласить на ваш сервер и многое другое.
  paused:
    desc: |
      ### {clock_emoji} Соединение приостановлено
      Приостановлено соединение для {channel}! Сообщения из хаба больше не будут отправляться в канал и ваши сообщения не будут попадать туда.
    tips: |
      **💡 Совет:** Используйте {unpause_cmd} чтобы снять с паузы соединение или {leave_cmd} чтобы полностью отключить его.
hub:
  notFound: '{emoji} Невозможно найти хаб. Убедитесь что вы ввели правильное название.'
  notFound_mod: '{emoji} Невозможно найти хаб. Убедитесь что вы ввели правильное название и являетесь владельцем/модератором хаба.'
  notManager: '{emoji} Вы должны быть менеджером хаба чтобы это сделать.'
  notModerator: '{emoji} Вы должны быть модератором хаба чтобы это сделать.'
  notPrivate: '{emoji} Этот хаб не приватный.'
  notOwner: '{emoji} Только владелец хаба может это сделать.'
  alreadyJoined: '{emoji} You have already joined **{hub}** from {channel}.'
  invalidChannel: '{emoji} Некорректный канал. Только текстовые каналы и ветки поддерживаются!'
  invalidImgurUrl: '{emoji} Некорректный URL изображения для иконки или баннера. Пожалуйста, убедитесь что вы ввели правильный URL изображения из Imgur, который не является галереей или альбомом.'
  join:
    success: |
      Успешно присоединился к хабу **{hub}** из {channel}! Теперь вы можете общаться с людьми из других серверов в этом канале.
      - Используйте `/connection` чтобы просмотреть различные опции кастомизации для этого соединения.
      - Используйте `/disconnect` чтобы прекратить получать сообщения из этого хаба.
      - Используйте **`/connection edit`** чтобы менять каналы.
  servers:
    total: 'Текущие подключённые сервера: {from}-{to} / **{total}**'
    noConnections: '{emoji} Еще ни один сервер не присоединился к этому хабу. Используйте `/connect` чтобы присоединиться к этому хабу.'
    notConnected: "{emoji} Этот сервер не является частью **{hub}**."
    connectionInfo: |
      ID Сервера: {serverId}
      Канал: #{channelName} `({channelId})`
      Когда Присоединились: {joinedAt}
      Приглашение: {invite}
      Соединено: {connected}
  blockwords:
    deleted: '{emoji} Правило фильтра успешно удалено!'
    notFound: '{emoji} Правило фильтра не найдено.'
    maxRules: '{emoji} Вы достигли максимального кол-ва правил (2) для фильтра этого хаба. Пожалуйста, удалите правило перед тем как добавлять ещё одно.'
    configure: 'Настройте действия для правила: {rule}'
    actionsUpdated: '{emoji} Обновлены действия для правила. **Новые Действия:** {actions}'
    selectRuleToEdit: Выберите правила, чтобы изменить его слова/действия
    listDescription: |
      ### {emoji} Правила Фильтра
      Этот хаб имеет {totalRules}/2 правил фильтра.
    listFooter: Выберите правило используя меню, чтобы просмотреть его полную информацию.
    ruleDescription: |
      ### {emoji} Редактирование Правила: {ruleName}
      {words}
    ruleFooter: 'Нажмите на кнопку ниже, чтобы изменить слова или названия правил!'
    actionSelectPlaceholder: 'Выберите действия для правила.'
    embedFields:
      noActions: '{emoji} **Отсутствует!** Настройте используя меню ниже.'
      actionsName: 'Настраиваемые Действия:'
      actionsValue: '{actions}'
    modal:
      addRule: Добавить Правило Фильтра
      editingRule: Редактирование Правила Фильтра
      ruleNameLabel: Название Правила
      wordsLabel: 'Слова'
      wordsPlaceholder: 'Слова через запятую (* = любой). Например: слово1, *слово2, *слово3, слово4*'
    validating: '{emoji} Проверка правила фильтра...'
    noRules: |
      ### {emoji} Let's set up some anti-swear rules!
      Use the `Add Rule` button to create one.
  create:
    modal:
      title: Создать Хаб
      name:
        label: Название Хаба
        placeholder: Введите название Хаба.
      description:
        label: Описание
        placeholder: Введите описание для вашего хаба.
      icon:
        label: URL Иконки
        placeholder: Введите URL Изображения из Imgur.
      banner:
        label: URL Баннера
        placeholder: Введите URL Изображения из Imgur.
    maxHubs: '{emoji} You have reached the maximum number of hubs ({maxHubs}) you can create. Please delete a hub before creating another one. [Vote for InterChat]({voteUrl}) to create more hubs.'
    invalidName: '{emoji} Некорректное название хаба. Оно не должно содержать `discord`, `clyde` или \`\`\`. Пожалуйста, выберите другое название.'
    nameTaken: '{emoji} Название этого хаба уже занято. Пожалуйста, выберите другое.'
    success: |
      ## Hub Created! It is __private__ by default.
      Use `/hub edit hub:{name}` to customize your hub. Please follow the steps below to get started:
      ### Next Steps:
      1. **Create an Invite:**
      > Use `/hub invite create` to create an invite for others to join.
      2. **Link a Channel:**
      > Use `/connect` **with the invite link previously generated** to link a channel to the hub and start chatting.
      3. **Configure Hub:** (Recommended)
      > Use `/hub config settings`, `/hub config logging` & `/hub config anti-swear` to configure the hub.
      4. **Add Moderators:**
      > Use `/hub moderator add` to add moderators to the hub.
      5. **Customize Hub:**
      > Use `/hub edit` to change the hub icon, banner, and description.
      6. **Go Public:**
      > Use `/hub visibility` to make the hub public and allow others to browse and join it without using invites. (Optional)

      If you have any questions or need help, feel free to ask in the [support server]({support_invite}). Consider [donating]({donateLink}) to support the development costs.
  delete:
    confirm: Вы уверены, что вы хотите удалить **{hub}**? Это действие не обратимо. Все присоединённые сервера, приглашения и данные о сообщениях будут удалены из этого хаба.
    ownerOnly: '{emoji} Только владелец этого хаба может его удалить.'
    success: '{emoji} Хаб **{hub}** был удалён.'
    cancelled: '{emoji} Удаление хаба было отменено.'
  browse:
    joinConfirm: |
      Вы уверены, что хотите присоединиться к {hub} из {channel}?

      **Примечание:** Вы всегда можете это изменить с помощью `/connection`.
    joinFooter: Хотите использовать другой канал? Используйте меню ниже.
    noHubs: '{emoji} Тут нет хабов в списке в данный момент. Попробуйте ещё раз позже!'
    rating:
      invalid: Некорректная оценка. Вы должны ввести число от 1 до 5.
      success: Оценка отправлена! Спасибо за обратную связь.
  invite:
    create:
      success: |
        ###Приглашение создано!

        Ваше приглашение было успешно создано. Другие сервера могут присоединиться к этому хабу используя команду `/connect`.

        - **Присоединитесь с помощью:** `/connect invite:{inviteCode}`
        - **Просмотрите приглашения:** `/hub invite list`
        - **Истекает:** {expiry}
        - **Использования**: ∞

        **Примечание:** Вы можете отозвать это приглашение в любое время с помощью `/hub invite revoke {inviteCode}`.
    revoke:
      invalidCode: '{emoji} Некорректный код приглашения. Пожалуйста, убедитесь что вы ввели правильный код приглашения.'
      success: '{emoji} Приглашение {inviteCode} отозвано.'
    list:
      title: '**Коды Приглашения:**'
      noInvites: '{emoji} Этот хаб еще не имеет приглашений. Используйте `/hub invite create` чтобы создать одно.'
      notPrivate: '{emoji} Только приватные хабы могут иметь приглашения. Используйте `/hub edit` чтобы сделать этот хаб приватным.'
  joined:
    noJoinedHubs: '{emoji} Этот сервер еще не присоединился ни к одному хабу. Используйте `/hub browse` чтобы просмотреть список хабов.'
    joinedHubs: Этот сервер является частью **{total}** хаба(-ов). Используйте `/disconnect` чтобы выйти из хаба.
  leave:
    noHub: '{emoji} Этот канал некорректный или не соединён ни с каким хабом.'
    confirm: Вы уверены, что хотите покинуть **{hub}** из {channel}? Сообщения больше не будут отправляться на этот сервер из хаба.
    confirmFooter: Подтвердите используя кнопку ниже в течение 10 секунд.
    success: '{emoji} Покинут хаб из {channel}. Сообщения больше не будут отправляться на этот сервер из хаба. Вы можете перезайти используя `/connect`.'
  moderator:
    noModerators: '{emoji} У этого хаба еще нет модераторов. Используйте `/hub moderator add` чтобы добавить одного.'
    add:
      success: '{emoji} **{user}** был добавлен как модератор на позицию **{position}**.'
      alreadyModerator: '{emoji} **{user}** уже модератор.'
    remove:
      success: '{emoji} **{user}** был удален в качестве модератора.'
      notModerator: '{emoji} **{user}** не модератор.'
      notOwner: '{emoji} Только владелец хаба может удалить менеджера.'
    update:
      success: "{emoji} Позиция **{user}** была обновлена до **{position}**."
      notModerator: "{emoji} **{user}** не модератор."
      notAllowed: "{emoji} Только менеджеры хаба могут обновлять позицию модератора."
      notOwner: "{emoji} Только владелец этого хаба может обновлять позицию менеджера."
  manage:
    dashboardTip: "**🛠️ NEW Dashboard:** Improved interface and more features! Try it out at [your hub's dashboard page]({url})."
    enterImgurUrl: Введите корректный URL изображения Imgur который не является галереей или альбомом.
    icon:
      changed: Иконка хаба успешно обновлена.
      modal:
        title: Редактировать Иконку
        label: URL Иконки
      selects:
        label: Редактировать Иконку
        description: Изменить иконку этого хаба.
    description:
      changed: Описание хаба успешно изменено.
      modal:
        title: Изменить Описание
        label: Описание
        placeholder: Введите описание этого хаба.
      selects:
        label: Измените Описание
        description: Измените описание этого хаба.
    banner:
      changed: Баннер хаба успешно изменён.
      removed: Баннер хаба успешно удалён.
      modal:
        title: Редактировать Баннер
        label: URL Баннера
      selects:
        label: Редактировать Баннер
        description: Изменить баннер этого хаба.
    visibility:
      success: '{emoji} Видимость хаба успешно изменена на **{visibility}**.'
      selects:
        label: Изменить Видимость
        description: Сделайте этот хаб публичным или приватным.
    toggleLock:
      selects:
        label: 'Заблокировать/Разблокировать Хаб'
        description: 'Заблокируйте или разблокируйте чаты хаба'
      confirmation: 'Чаты хаба теперь {status}.'
      announcementTitle: 'Чаты хаба теперь {status}.'
      announcementDescription:
        locked: 'Только модераторы могут отправлять сообщения.'
        unlocked: 'Все могут отправлять сообщения.'
    embed:
      visibility: 'Видимость'
      connections: 'Соединения'
      chatsLocked: 'Чаты заблокированы'
      blacklists: 'Чёрный список'
      total: 'Всего'
      users: 'Пользователи'
      servers: 'Сервера'
      hubStats: 'Статистика Хаба'
      moderators: 'Модераторы'
      owner: 'Владелец'
    logs:
      title: Настройка логов
      reset: '{emoji} Логи конфигурации успешно сброшены для `{type}`.'
      roleSuccess: '{emoji} Логи `{type}` теперь будут упоминать {role}!'
      roleRemoved: '{emoji} Логи `{type}` теперь не будут упоминать роль.'
      channelSuccess: '{emoji} Логи `{type}` теперь будут отправляться в  {channel}!'
      channelSelect: '#️⃣ Выберите канал для отправки логов'
      roleSelect: '🏓 Выберите роль, которая будет упоминаться в логах.'
      reportChannelFirst: '{emoji} Пожалуйста, сначала выберите канал для логов.'
      config:
        title: Настройте  Логи `{type}`
        description: |
          {arrow} Выберите канал логов и/или роль которая будет упоминаться в меню ниже
          {arrow} Вы также можете отключить логи в меню ниже.
        fields:
          channel: Канал
          role: Упоминание Роли
      reports:
        label: Жалобы
        description: Получайте жалобы от пользователей.
      modLogs:
        label: Логи Модераторов
        description: 'Записывать Действия Модераторов. (Например: добавление в чёрный список, удаление сообщений, и тд.)'
      joinLeaves:
        label: Вступления/Уходы
        description: Записывать когда сервер заходит или уходит из этого хаба.
      appeals:
        label: Обжалования
        description: Получать обжалования от пользователей/серверов в чёрном списке.
      networkAlerts:
        label: Оповещения Сети
        description: Получать оповещения об автоматически заблокированных сообщениях.
  transfer:
    invalidUser: '{emoji} Указанный пользователь не найден.'
    selfTransfer: '{emoji} Вы не можете передать права владельца самому себе.'
    botUser: '{emoji} Вы не можете передать права владельца боту.'
    confirm: 'Вы уверены, что хотите передать права владельца **{hub}** пользователю {newOwner}? Вы будете понижены до роли менеджера.'
    cancelled: '{emoji} Передача хаба отменена.'
    error: '{emoji} Возникла ошибка при передаче хаба.'
    success: '{emoji} Успешно переданы права владельца **{hub}** пользователю {newOwner}. Вы были добавлены как менеджер.'
    timeout: '{emoji} Вышло время на передачу хаба.'
  rules:
    noRules: "{emoji} Этот хаб пока не имеет настроенных правил. Давайте добавим их!"
    list: "### {emoji} Правила Хаба\n{rules}"
    maxRulesReached: "{emoji} Достигнуто максимальное количество правил ({max})."
    ruleExists: "{emoji} Это правило уже существует."
    selectedRule: "Выбрано правило {number}"
    modal:
      add:
        title: Добавить Правило
        label: Текст правил
        placeholder: Введите текст правил (не более 1000 символов)
      edit:
        title: Изменить Правила Хаба
        label: Текст правил
        placeholder: Введите новый текст правил (не более 1000 символов)
    select:
      placeholder: Выберите правило для редактирования или удаления
      option:
        label: Правило {number}
    buttons:
      add: Добавить Правило
      edit: Редактировать Правило
      delete: Удалить Правило
      back: Назад
    success:
      add: '{emoji} Правило успешно добавлено!'
      edit: 'Правило {emoji} успешно изменено!'
      delete: '{emoji} Правило успешно удалено!'
    view:
      title: 'Правило {number}'
      select: Выберите действие для этого правила
  welcome:
    set: '{emoji} Welcome message updated successfully!'
    removed: '{emoji} Welcome message removed.'
    voterOnly: '{emoji} Custom welcome messages are a voter-only perk! Vote to unlock this feature.'
    placeholder: |
      Welcome {user} from {serverName} to {hubName}! 🎉
      Members: {memberCount}, Hub: {totalConnections}!
report:
  modal:
    title: Подробности Жалобы
    other:
      label: Подробности Жалобы
      placeholder: Подробное описание жалобы.
    bug:
      input1:
        label: Подробности Бага
        placeholder: Например. Частые сбои взаимодействия команды /help...
      input2:
        label: Подробное описание (необязательно)
        placeholder: Шаги, которые вы сделали. Например, 1.Использовал /help 2.Подождал 5 секунд... 
  reasons:
    spam: Spam or excessive messages
    advertising: Unwanted advertising or self-promotion
    nsfw: NSFW or inappropriate content
    harassment: Harassment or bullying
    hate_speech: Hate speech or discrimination
    scam: Scam, fraud, or phishing attempt
    illegal: Illegal content or activities
    personal_info: Sharing personal/private information
    impersonation: Impersonating others
    breaks_hub_rules: Violates hub rules
    trolling: Trolling or intentional disruption
    misinformation: False or misleading information
    gore_violence: Gore or extreme violence
    raid_organizing: Organizing raids or attacks
    underage: Underage user or content
  dropdown:
    placeholder: Select a reason for your report
  submitted: '{emoji} Жалоба успешно отправлена. Присоединитесь к {support_command} чтобы получить больше подробностей. Спасибо!'
  bug:
    title: Отчет об Ошибках
    affected: Затронутые Компоненты
    description: Пожалуйста, выберите компонент бота с которым у вас наблюдаются проблемы.
language:
  set: Язык выбран! Теперь я буду обращаться к вам на {lang}.
errors:
  messageNotSentOrExpired: '{emoji} Это сообщение не было отправлено в хаб, оно истекло или вы обладаете недостаточными правами чтобы его отправить.'
  notYourAction: "{emoji} Извините, вы не можете этого сделать. Запустите команду сами."
  notMessageAuthor: '{emoji} Вы не автор этого сообщения.'
  commandError: |
    {emoji} Произошла ошибка при выполнении команды. Она была записана в нашу систему. Если ошибка повторится то зайдите на наш [сервер поддержки]({support_invite}) и сообщите ID ошибки.

    **ID Ошибки:**
    ```{errorId}```
  mustVote: Пожалуйста [проголосуйте](https://top.gg/bot/769921109209907241/vote) за InterChat, чтобы использовать эту команду, ваша поддержка очень ценится!
  inviteLinks: '{emoji} Вы не можете отправлять ссылки приглашения в этот хаб. Установите приглашение используя `/connection` вместо этого! Модераторы хаба могут изменять это используя `/hub edit settings`'
  invalidLangCode: '{emoji} Некорректный код языка. Убедитесь что вы ввели правильный [код языка](https://cloud.google.com/translate/docs/languages).'
  unknownServer: '{emoji} Неизвестный сервер. Пожалуйста, убедитесь что вы ввели правильный **ID Сервера**.'
  unknownNetworkMessage: '{emoji} Неизвестное сообщение. Если оно было отправлено минуту и менее назад попробуйте еще раз через несколько секунд.'
  userNotFound: '{emoji} Пользователь не найден. Попробуйте ввести его ID.'
  blacklisted: '{emoji} Вы или этот сервер были внесены в чёрный список в хабе {hub}.'
  userBlacklisted: '{emoji} Вы были внесены в чёрный список этого хаба.'
  serverBlacklisted: '{emoji} Этот сервер внесён в черный список этого хаба.'
  serverNotBlacklisted: '{emoji} Введённый сервер не внесён в чёрный список.'
  userNotBlacklisted: '{emoji} Введённый пользователь не внесён в чёрный список.'
  missingPermissions: '{emoji} Вам не хватает следующих прав для совершения этого действия: **{permissions}**'
  botMissingPermissions: '{emoji} Пожалуйста, дайте мне следующие права для продолжения: **{permissions}**'
  unknown: '{emoji} Произошла неизвестная ошибка. Пожалуйста, попробуйте снова позже или свяжитесь с нами в [сервере поддержки]({support_invite}).'
  notUsable: '{emoji} Это больше не используется.'
  cooldown: '{emoji} Вы превысили лимит действий. Пожалуйста, подождите до **{time}** прежде чем попробовать снова.'
  serverNameInappropriate: '{emoji} Имя вашего сервера содержит неуместные слова. Пожалуйста, измените его прежде чем как присоединиться к хабу.'
  banned: |
    {emoji} Вы были заблокированы от использования InterChat за нарушение наших [правил](https://interchat.tech/guidelines).
    Если вы думаете что обжалование уместно то создайте запрос на нашем [сервере поддержки]( {support_invite} ).
config:
  setInvite:
    success: |
      ### {emoji} Сссылка Приглашения Установлена
      - Приглашение на ваш сервер будет использовано когда пользователи будут использовать команду `/joinserver`.
      - Оно будет отображено в `/leaderboard server`.
    removed: '{emoji} Приглашение успешно убрано!'
    invalid: '{emoji} Некорректное приглашение. Пожалуйста, убедитесь что вы ввели правильную ссылку. Например, `https://discord.gg/discord`'
    notFromServer: '{emoji} Это приглашение не из этого сервера.'
badges:
  shown: '{emoji} Your badges will now be shown in messages.'
  hidden: '{emoji} Your badges will now be hidden in messages.'
  command:
    description: '🏅 Configure your badge display preferences'
    options:
      show:
        name: 'show'
        description: 'Whether to show or hide your badges in messages'
  list:
    developer: 'Core developer of InterChat'
    staff: 'InterChat staff member'
    translator: 'Translator of InterChat'
    voter: 'Voted for InterChat in the last 12 hours'
global:
  webhookNoLongerExists: '{emoji} Вебхук для этого канала более не существует. Чтобы продолжить использовать InterChat, пожалуйста создайте вебхук заново используя `/connection unpause`.'
  noReason: Причина не указана.
  noDesc: Нет описания.
  version: InterChat v{version}
  loading: '{emoji} Пожалуйста, подождите пока я обрабатываю ваш запрос...'
  reportOptionMoved: '{emoji} Эта опция была перемещена! Чтобы пожаловаться на сообщение модераторам хаба, используйте обновленную команду `Apps > Message Info/Report`. Чтобы пожаловаться на прямую сотрудникам InterChat, просто зайдите на наш [сервер поддержки]({support_invite}) и создайте запрос с доказательствами.'
  private: 'Приватный'
  public: 'Публичный'
  yes: 'Да'
  no: 'Нет'
  cancelled: '{emoji} Отменено. Изменения не были сделаны.'
warn:
  modal:
    title: Выдать Предупреждение Пользователю
    reason:
      label: Причина
      placeholder: Введите причину предупреждения этого пользователя...
  success: |
    {emoji} Успешно выдано предупреждение **{name}**.

    -# Пользователь будет уведомлен о последнем предупреждении при следующем отправке сообщения в хабе. Избегайте выдачи нескольких предупреждений сразу.
  dm:
    title: '{emoji} Уведомление о Предупреждении'
    description: 'Вам выдано предупреждение в хабе **{hubName}**'
  log:
    title: '{emoji} Пользователю Выдано Предупреждение'
    description: |
      {arrow} **Пользователь:** {user} ({userId})
      {arrow} **Модератор:** {moderator} ({modId})
      {arrow} **Причина:** {reason}
    footer: 'Предупреждение выдано модератором {moderator}'
