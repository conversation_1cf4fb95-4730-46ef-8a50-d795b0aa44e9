rules:
  header: 'InterChat 规则'
  botRulesNote: '这些规则的制定是为了给每个人带来安全且愉快的体验。请仔细阅读并遵守以下规则：'
  rules: |
    1. **禁止仇恨言论或骚扰行为**
    -# > **包括：** 使用侮辱性言论或仇恨言论攻击他人，[及其他行为]({guidelines_link})。
    2. **禁止非法内容**
    -# > **包括：** 分享非法内容的链接，煽动暴力，[及其他行为]({guidelines_link})。
    3. **禁止重度成人或血腥暴力内容**
    -# > **包括：** 在InterChat发布血腥暴力内容，在非成人内容中心发布成人内容，[及其他行为]({guidelines_link})。
    4. **禁止发送垃圾信息或刷屏行为**
    -# > **包括：** 大量发送垃圾信息或使用工具进行刷屏，[及其他行为]({guidelines_link})。
    5. **禁止假冒或欺诈他人**
    -# > **包括：** 假冒InterChat的工作人员或中心的监管员，进行虚拟货币或NFT诈骗活动，[及其他行为]({guidelines_link})。
    6. **禁止剥削或虐待行为**
    -# > **包括：** 对未成年人进行诱骗或性剥削行为，还有分享、索取、敲诈或威胁行为，或煽动自残，[及其他行为]({guidelines_link})。
    7. **禁止分享恶意软件**
    -# > **包括：** 分享恶意软件、病毒、“免费Nitro”链接或有害脚本，[及其他行为]({guidelines_link})。

    请同意遵守 [Discord的服务条款](https://discord.com/terms) 以及 [社区准则](https://discord.com/guidelines)。查看 [完整规则列表]({guidelines_link})。
  welcome: |
    {emoji}您好，{user}！准备好和其他社区连接了吗？在开始跨服务器聊天之前，请快速回顾一下我们的社区准则。这些准则有助于InterChat为每个人维护良好环境。
  alreadyAccepted: '{emoji}您已接受所有规则。您现在可以尽情使用InterChat。'
  continue: 继续
  accept: 接受
  decline: 拒绝
  agreementNote: 接受这些规则，则表明您在使用InterChat期间同意遵守所有规则。若违反规则可能会产生限制或禁令。
  hubAgreementNote: |
    接受这些规则，则表明您在此中心聊天期间同意遵守所有规则。若违反规则您可能会被此中心移除。

    ⚠️ **只有接受这些规则，您才能在此中心发送消息。**
  accepted: |
    {emoji} 准备完毕！欢迎来到InterChat社区。

    快速入门提示：
    - 使用“/浏览中心”查找活跃社区
    - 加入您感兴趣的讨论
    - 使用我们新的“/呼叫”指令以连接单个服务器

    需要帮助吗？加入我们的 [社区]({support_invite}) - 我们很乐意提供帮助！

    喜欢我们的平台吗？请考虑一下 [支持我们]({donateLink}) ，助力InterChat持续发展。
  declined: |
    {emoji} 您已拒绝InterChat的规则。
    -# ⚠️ **只有接受规则后，您才能使用InterChat或者进行跨服务器聊天。**
    -# 如重试，请另发送一条消息或使用任意InterChat的指令。
  hubAccepted: |
    {emoji}您已接受中心规则。现在可以在此中心开始聊天了！
  hubDeclined: |
    {emoji} 您已拒绝{hubName}的规则。
    -# ⚠️ **只有接受中心规则后，您才能在此中心发送消息。**
    -# 如重试，请在此中心另发送一条消息。
  noHubRules: 此中心尚未设置任何细则。但是 [InterChat通用规则]({rules_link}) 仍然适用。
  hubRules: 中心规则
  viewbotRules: "View Bot Rules"
vote:
  description: |
    帮助更多社区发现InterChat！您在top.gg的投票将:
    - 帮助其他人找到活跃社区
    - 为您解锁特殊功能
    - 支持我们的独立发展
  footer: "每隔12小时刷新一轮投票 • 感谢您对InterChat的支持！"
  button:
    label: "在top.gg进行投票"
  perks:
    moreComingSoon: "更多福利即将推出！关于福利，有任何建议请在[支持服务器]({support_invite})提出。"
network:
  accountTooNew: '{emoji}{user}您的账号注册时间不足，无法在InterChat发送消息。请稍后再试。'
  deleteSuccess: '{emoji}{user}发送的消息已从 __**{deleted}/{total}**__ 服务器中删除。'
  editInProgress: '{emoji} Your request has been queued. Messages will be edited shortly...'
  editInProgressError: '{emoji} This message is already being edited by another user.'
  emptyContent: '{emoji} Message content cannot be empty.'
  newMessageContent: 'New Message Content'
  editMessagePrompt: '{emoji} Please use the modal to edit your message.'
  editSuccess: '{emoji}{user}发送的消息已在__**{edited}／{total}**__服务器中完成编辑。'
  onboarding:
    embed:
      title: '👋 欢迎来到 {hubName}！'
      description: |
        您已找到一个活跃社区中心！为保证愉快友好的对话体验，请快速浏览准则后再加入讨论。
      footer: InterChat网络｜版本{version}
    inProgress: '{emoji}{channel}加入中心的流程正在进行中。您启动安装后，请等待安装完成或取消安装。'
blacklist:
  success: '{emoji}**{name}**已成功列入黑名单！'
  removed: '{emoji}**{name}**已从黑名单中移除！'
  modal:
    reason:
      label: 原因
      placeholder: 列入黑名单的原因
    duration:
      label: 持续时间
      placeholder: '黑名单的持续时间。例如：一天、一周、一个月、一年。留空表示永久。'
  user:
    cannotBlacklistMod: '{emoji}您不能将监管员列入黑名单。请先移除他们的监管员身份。'
    alreadyBlacklisted: '{emoji}该用户已被列入黑名单。'
    easterEggs:
      blacklistBot: 你不能把我列入黑名单。
  server:
    alreadyBlacklisted: '{emoji}此服务器已被列入黑名单。'
    unknownError: 未能将{server}列入黑名单。向开发者咨询更多信息。
  list:
    user: |
      **用户名称**：{id}
      **监管员：**：{moderator}
      **原因**：{reason}
      **期限**：{expires}
    server: |
      服务器名称：{id}
      监管员：{moderator}
      原因：{reason}
      期限：{expires}
msgInfo:
  buttons:
    message: 信息
    server: 服务器信息
    user: 用户信息
    report: 报告
  report:
    notEnabled: '{emoji}此中心未启用报告功能。'
    success: '{emoji}报告已成功提交。非常感谢！'
invite: |
  感谢您选择邀请InterChat！如果您有任何问题或需要帮助，我们随时在支持服务器上为您提供帮助！

  [{invite_emoji}“邀请链接”]({invite})[{support_emoji}“支持服务器”]({support})
connection:
  joinRequestsDisabled: '{emoji}此中心暂不开放加入申请。'
  notFound: '{emoji}连接无效。请检查频道名称或从显示的选项中选择。'
  channelNotFound: '{emoji}无法找到目标频道。请重新选择频道。'
  alreadyConnected: '{emoji}频道{channel}已连接中心。'
  switchChannel: '{emoji}请通过下方的选择菜单来选择目标频道：'
  switchCalled: '{emoji}频道切换已调用，请再次使用该指令查看新连接。'
  switchSuccess: '{emoji}频道已切换。您现在已从{channel}连接。'
  inviteRemoved: '{emoji}已移除此中心的服务器邀请。'
  setInviteError: '{emoji}无法创建邀请。请授予我在已连接频道的“创建邀请”权限。'
  inviteAdded: '{emoji}邀请已添加。其他人现在可通过使用“应用程序＞信息／报告”指令和“／加入服务器”指令来加入此服务器。'
  emColorInvalid: '{emoji}颜色无效。请确保您输入了有效的十六进制颜色代码。'
  emColorChange: '{emoji}嵌入颜色成功{action}'
  embed:
    title: 连接详情
    fields:
      hub: 中心
      channel: 频道
      invite: 邀请
      connected: 已连接
      emColor: 嵌入颜色
      compact: 简洁模式
    footer: 使用下方的下拉列表管理您的连接。
  selects:
    placeholder: '🛠️ 选择一个选项来编辑此连接'
  unpaused:
    desc: |
      {tick_emoji}已恢复连接

      {channel}的连接已恢复！来自此中心的信息将开始进入此频道，并且您可以再次发送信息到中心。
    tips: |
      提示：使用{pause_cmd}来中断连接，或使用{edit_cmd}来设置嵌入颜色、邀请到您的服务器等。
  paused:
    desc: |
      {clock_emoji}已中断连接
      {channel}的连接已中断！来自此中心的信息将不再进入此频道，并且您的信息将不会发送出去。
    tips: |
      提示：使用{unpause_cmd}来恢复连接，或使用{leave_cmd}来永久停收信息。
hub:
  notFound: '{emoji}无法找到中心。请确保您输入了正确的中心名称。'
  notFound_mod: '{emoji}无法找到中心。请确保您输入了正确的中心名字以及您是此中心的所有者或监管员。'
  notManager: '{emoji}您必须是中心管理员才能执行此操作。'
  notModerator: '{emoji}您需要成为中心监管员才能执行此操作。'
  notPrivate: '{emoji}此中心非私有。'
  notOwner: '{emoji}只有此中心的所有者可以执行此操作。'
  alreadyJoined: '{emoji} 您已从 {channel}加入另一个中心**{hub}** ！使用“/断开连接”后再次尝试使用“/连接”。'
  invalidChannel: '{emoji}频道无效。仅支持文本频道和对话线程频道！'
  invalidImgurUrl: '{emoji}图标或标语的图片网址无效。请确保您输入了有效的Imgur图片网址且该网址不是图库或相册。'
  join:
    success: |
      成功从{channel}加入了中心**{hub}**！您现在可以和此频道其他服务器上的成员聊天了。
      - 使用“/连接”来探索此连接的各种自定义设置。
      - 使用“/断开连接”来停收此中心的消息。
      - 使用**“/编辑连接”**来切换频道。
  servers:
    total: '目前已连接的服务器：{from}-­­­­­­­­­­{to}­／{total}'
    noConnections: '{emoji}尚未有服务器加入此中心。使用“/连接”加入此中心。'
    notConnected: "{emoji}目标服务器未连接{hub}。"
    connectionInfo: |
      服务器名称：{serverId}
      频道：#{channelName}“({channelId})”
      加入时间：{joinedAt}
      邀请：{invite}
      已连接：{connected}
  blockwords:
    deleted: '{emoji}过滤规则已成功删除！'
    notFound: '{emoji}未找到过滤规则。'
    maxRules: '{emoji}已达到此中心的过滤规则最大数量（2条）。请删除旧规则后新增。'
    configure: '{rule}规则配置'
    actionsUpdated: '{emoji}已更新此规则将进行的操作。新操作：{actions}'
    selectRuleToEdit: 选择一条规则来编辑关键词／操作
    listDescription: |
      {emoji}过滤规则
      此中心已配置{totalRules}／2条过滤规则。
    listFooter: 选择一条规则并用菜单查看详情。
    ruleDescription: |
      {emoji}编辑规则：{ruleName}{words}
    ruleFooter: '点击下方按钮来编辑关键词或规则名称！'
    actionSelectPlaceholder: '选择此规则应该执行的操作。'
    embedFields:
      noActions: '{emoji}无配置！请通过下方菜单进行配置。'
      actionsName: '配置操作：'
      actionsValue: '{actions}'
    modal:
      addRule: 添加过滤规则
      editingRule: 编辑过滤规则
      ruleNameLabel: 规则名称
      wordsLabel: '关键词'
      wordsPlaceholder: '用逗号分隔关键词。(使用 * 作为通配符)。例如：关键词一，*关键词二*，*关键词三，关键词四*'
    validating: '{emoji}正在校验过滤规则……'
    noRules: |
      ### {emoji} 让我们制定一些过滤规则吧！
      通过“添加规则”按钮进行创建。
  create:
    modal:
      title: 创建中心
      name:
        label: 中心名称
        placeholder: 为您的中心输入一个名称。
      description:
        label: 描述
        placeholder: 为您的中心输入描述。
      icon:
        label: 图标网址
        placeholder: 输入一个Imgur图片网址。
      banner:
        label: 标语网址
        placeholder: 输入一个Imgur图片网址。
    maxHubs: '{emoji} 已达到可以创建中心的最大数量({maxHubs})。请删除旧中心后新建。[为InterChat投票]({voteUrl}) 可以创建更多中心。'
    invalidName: '{emoji}中心名称无效。名称不能包含“discord”、“clyde”或“＼”。请另选名称。'
    nameTaken: '{emoji}此中心名称已被占用。请另选名称。'
    success: |
      ## 中心已创建！默认情况为__private__。
      使用“/编辑中心 中心：{name}”来自定义您的中心。请按照以下步骤开始操作：
      ### 接下来的步骤：
      1. **创建邀请：**
      > 使用“/创建中心邀请”来创建让其他人加入的邀请。
      2. **关联频道：**
      > 使用“/连接”同时**附上之前生成的邀请链接**，将一个频道关联到此中心并开始聊天。
      3. **设置中心：**（推荐）
      > 使用“/中心配置设定”、“/中心配置记录”和“/中心过滤规则配置”来设置此中心。
      4. **添加监管员：**
      > 使用“/添加中心监管员”来为此中心添加监管员。
      5. **自定义中心：**
      > 使用“/编辑中心”来更改此中心的图标、标语和描述。
      6. **公开中心：**
      > 使用“/中心可见”将中心设为公开，让其他人不用邀请也可以浏览并加入。(可选)

      如果您有任何问题或需要帮助，请随时在 [支持服务器]({support_invite})中提问。请考虑一下 [捐赠]({donateLink})来支持开发成本。
  delete:
    confirm: 您确定要删除{hub}吗？该行动无法撤回。所有已连接的服务器、邀请和消息数据都将从此中心删除。
    ownerOnly: '{emoji}只有此中心的所有者可以删除它。'
    success: '{emoji}中心{hub}已被删除。'
    cancelled: '{emoji}删除中心已取消。'
  browse:
    joinConfirm: |
      您确定要从{channel}加入{hub}吗？

      注意：此后您始终可以使用“/连接”进行更改。
    joinFooter: 想要使用其他频道吗？请使用下方的下拉菜单。
    noHubs: '{emoji}目前此处未列出任何中心。请稍后再试！'
    rating:
      invalid: 评分无效。您必须输入一个1到5之间的数字。
      success: 评分已提交！感谢您的反馈。
  invite:
    create:
      success: |
        ### 邀请已创建！

        您的邀请已成功创建。其他人现在可以通过使用“/连接”指令加入此中心。

        - **加入方式：**“/连接邀请：{inviteCode}”
        - **查看邀请：**“/中心邀请列表”
        - **到期时间：**{expiry}
        - **使用次数**：无限

        **注意：**您可以随时使用“/中心邀请撤回{inviteCode}”来撤回此邀请。
    revoke:
      invalidCode: '{emoji}邀请码无效。请确保您输入了有效的邀请码。'
      success: '{emoji}邀请{inviteCode}撤回。'
    list:
      title: '邀请码'
      noInvites: '{emoji}此中心尚未收到邀请。使用“／创建中心邀请”来创建邀请。'
      notPrivate: '{emoji}只有私人中心才能收到邀请。使用“／编辑编辑”将此中心设置为私有。'
  joined:
    noJoinedHubs: '{emoji}此服务器尚未加入如何中心。使用“／浏览中心”查看中心列表。'
    joinedHubs: 此服务器已连接**{total}**中心(群)。使用“/断开连接”离开中心。
  leave:
    noHub: '{emoji}该频道无效或尚未加入任何中心。'
    confirm: 您确定想要从{channel}离开{hub}吗？此中心将不再向此服务器发送任何消息。
    confirmFooter: 请在10秒内使用下面的按钮进行确认。
    success: '{emoji}从{channel}离开中心。此中心将不再向此服务器发送任何消息。您可以使用“/连接”重新加入。'
  moderator:
    noModerators: '{emoji}此中心尚未有监管员。使用“／添加中心监管员”添加监管员。'
    add:
      success: '{emoji}{user}已被添加为{position}级监管员。'
      alreadyModerator: '{emoji}{user}已成为监管员。'
    remove:
      success: '{emoji}{user}已被移除监管员身份。'
      notModerator: '{emoji}{user}不是监管员。'
      notOwner: '{emoji}只有此中心的所有者可以移除管理员。'
    update:
      success: "{emoji}{user}的级别已更新为{position}。"
      notModerator: "{emoji}{user}不是监管员。"
      notAllowed: "{emoji}只有中心管理员可以更新监管员级别。"
      notOwner: "{emoji}只有此中心的所有者可以更新管理员的级别。"
  manage:
    dashboardTip: "**🛠️ NEW Dashboard:** Improved interface and more features! Try it out at [your hub's dashboard page]({url})."
    enterImgurUrl: 输入一个有效的Imgur图片网址，且该网址不是图库或相册。
    icon:
      changed: 中心图标已成功更改。
      modal:
        title: 编辑图标
        label: 图标网址
      selects:
        label: 编辑图标
        description: 更改此中心的图标。
    description:
      changed: 中心的描述成功更改。
      modal:
        title: 编辑描述
        label: 描述
        placeholder: 输入此中心的描述。
      selects:
        label: 更改描述
        description: 更改此中心的描述。
    banner:
      changed: 中心的标语已成功更改。
      removed: 中心的标语已被成功移除。
      modal:
        title: 编辑标语
        label: 标语网址
      selects:
        label: 编辑标语
        description: 更改此中心的标语。
    visibility:
      success: '{emoji}中心可见已成功更改为{visibility}。'
      selects:
        label: 可见性设置
        description: 将中心设为公开或私有。
    toggleLock:
      selects:
        label: '锁定／解锁中心'
        description: '关闭或打开此中心聊天区'
      confirmation: '中心聊天区现在为{status}。'
      announcementTitle: '中心聊天区现在为{status}。'
      announcementDescription:
        locked: '只有监管员可以发送信息。'
        unlocked: '所有人都可以发送信息。'
    embed:
      visibility: '可见性'
      connections: '连接'
      chatsLocked: '聊天区已关闭'
      blacklists: '黑名单'
      total: '总数'
      users: '用户'
      servers: '服务器'
      hubStats: '中心统计数据'
      moderators: '监管员'
      owner: '所有者'
    logs:
      title: 配置记录
      reset: '{emoji}成功重置“{type}”记录的配置记录。'
      roleSuccess: '{emoji}“{type}”记录现在将通知{role}！'
      roleRemoved: '{emoji}“{type}”记录将不再通知对象。'
      channelSuccess: '{emoji}“{type}”记录从现在开始将被发送至{channel}！'
      channelSelect: '#️⃣ 选择一个频道发送记录'
      roleSelect: '🏓 选择触发记录时通知的对象。'
      reportChannelFirst: '{emoji}请先设置一个记录频道。'
      config:
        title: '“{type}”配置记录'
        description: |
          {arrow}从下方的下拉列表中选择要通知的记录频道和／或对象。
          {arrow}您也可以通过下方按钮来停用记录功能。
        fields:
          channel: 频道
          role: 通知对象
      reports:
        label: 报告
        description: 从用户处收到报告。
      modLogs:
        label: 监管员记录
        description: 监管操作记录。（例如：列入黑名单、删除信息等等）
      joinLeaves:
        label: 加入／离开
        description: 当服务器加入或离开此中心时记录下来。
      appeals:
        label: 上诉
        description: 从被列入黑名单的用户／服务器处受理上诉。
      networkAlerts:
        label: 网络警报
        description: 受理关于自动屏蔽信息的警报。
  transfer:
    invalidUser: '{emoji}未找到指定用户。'
    selfTransfer: '{emoji}您不能把所有权转给自己。'
    botUser: '{emoji}您不能把所有权转给机器人。'
    confirm: '您确定要把**{hub}**的所有权转给{newOwner}吗？您将降为管理员身份。'
    cancelled: '{emoji}转移中心已取消。'
    error: '{emoji}转移中心所有权时发生了错误。'
    success: '{emoji}成功把{hub}的所有权转给{newOwner}。您已被添加为管理员。'
    timeout: '{emoji}转移中心已超时。'
  rules:
    noRules: "{emoji} 此中心尚未配置规则。添加一些规则吧！"
    list: "### {emoji} 中心规则\n{rules}"
    maxRulesReached: "{emoji}已达到规则最大数量({max}) 。"
    ruleExists: "{emoji}此规则已存在。"
    selectedRule: "选择规则{number}"
    modal:
      add:
        title: 添加中心规则
        label: 规则内容
        placeholder: 输入规则内容（最多一千字）
      edit:
        title: 编辑中心规则
        label: 规则内容
        placeholder: 输入新规则内容（最多一千字）
    select:
      placeholder: 选择要编辑或删除的规则
      option:
        label: 规则{number}
    buttons:
      add: 添加规则
      edit: 编辑规则
      delete: 删除规则
      back: 返回
    success:
      add: '{emoji}规则已成功添加！'
      edit: '{emoji}规则已成功更新！'
      delete: '{emoji}规则已成功删除！'
    view:
      title: '规则{number}'
      select: 选择此规则的操作
  welcome:
    set: '{emoji}欢迎信息已成功更新！'
    removed: '{emoji}欢迎信息已移除。'
    voterOnly: '{emoji}自定义欢迎信息是属于为我们投票的人独有的福利！去投票解锁此功能。'
    placeholder: |
      欢迎 {user} 从 {serverName} 来到 {hubName}! 🎉
      成员： {memberCount}，中心：{totalConnections}！
report:
  modal:
    title: 报告详情
    other:
      label: 报告详情
      placeholder: 报告中的一份详细描述。
    bug:
      input1:
        label: 故障详情
        placeholder: 例如，“／帮助” 指令频繁出现互动失败的情况……
      input2:
        label: 描述详情（可选）
        placeholder: 要做的步骤。例如：1.运行／帮助 2.等待5秒……
  reasons:
    spam: 垃圾或过多消息
    advertising: 垃圾推广
    nsfw: 成人或不恰当内容
    harassment: 骚扰或欺凌
    hate_speech: 侮辱性或仇恨言论
    scam: 诈骗、欺诈或网络钓鱼
    illegal: 非法的内容或活动
    personal_info: 泄露个人/私人信息
    impersonation: 仿冒他人
    breaks_hub_rules: 违反中心规则
    trolling: 拖钓或扰乱
    misinformation: 错误或误导性信息
    gore_violence: 血腥或极端暴力
    raid_organizing: 组织袭击
    underage: 未达年龄限制的用户或内容
  dropdown:
    placeholder: 选择您举报的原因
  submitted: '{emoji}报告已成功提交。加入{support_command}获取详情。谢谢！'
  bug:
    title: 故障报告
    affected: 受影响的部分
    description: 请选择出现问题的程序部分。
language:
  set: 语言设置！现在将用{lang}进行回应。
errors:
  messageNotSentOrExpired: '{emoji}此消息未在中心发送，已过期，或者您没有执行此操作的权限。'
  notYourAction: "{emoji}抱歉，您无法执行此操作。请由本人行此指令。"
  notMessageAuthor: '{emoji}您不是这条信息的发布者。'
  commandError: |
    {emoji}执行此指令时发生了错误。该错误已记录到我们的系统中。如果此问题持续存在，请加入我们的[支持服务器]({support_invite})并报告错误 名称！

    错误名称：
    ```{errorId}```
  mustVote: 请[投票](https://top.gg/bot/769921109209907241/vote)给InterChat以使用此指令，非常感谢您的支持！
  inviteLinks: '{emoji}您不能向此中心发送邀请链接。请改为在“／连接”处设置邀请！中心监管员可以用“／中心编辑设定”来配置'
  invalidLangCode: '{emoji}语言模式无效。请确保您输入了正确的[语言模式](https://cloud.google.com/translate/docs/languages)。'
  unknownServer: '{emoji}未知服务器。请确保您输入了正确的服务器名称。'
  unknownNetworkMessage: '{emoji}未知信息。如果该内容在过去一分钟内已发送过，请再等待几秒钟后重试。'
  userNotFound: '{emoji}找不到该用户。请尝试输入他们的名称。'
  blacklisted: '{emoji}您或者此服务器已被此中心{hub}列入黑名单。'
  userBlacklisted: '{emoji}您已被此中心列入黑名单。'
  serverBlacklisted: '{emoji}此服务器已被此中心列入黑名单。'
  serverNotBlacklisted: '{emoji}输入的服务器未被列入黑名单。'
  userNotBlacklisted: '{emoji}输入的用户未被列入黑名单。'
  missingPermissions: '{emoji}您没有执行此操作的以下权限：{permissions}'
  botMissingPermissions: '{emoji}请授予我以下权限以继续：{permissions}'
  unknown: '{emoji}出现一个未知错误。请稍后重试或通过我们的[支持服务器]({support_invite})来联系我们。'
  notUsable: '{emoji}这已无法再用。'
  cooldown: '{emoji}您处于冷却时间内。请等到{time}后再尝试。'
  serverNameInappropriate: '{emoji}您的服务器名称包含不当内容。请更改后再加入中心。'
  banned: |
    {emoji}由于违反了我们的[准则](https://interchat.tech/guidelines)，您已被禁止使用Interchat。
    如果您认为可以上诉，请在[支持服务器]({support_invite})创建票证。
config:
  setInvite:
    success: |
      ### {emoji} 邀请链接设置
      - 当用户使用“／加入服务器”指令时，将自动使用您的服务器邀请链接。
      - 该链接同时会在“／服务器排行榜”中显示。
    removed: '{emoji}邀请已成功移除！'
    invalid: '{emoji}邀请无效。请确保您输入了有效的邀请链接。例如：“https://discord.gg/discord”'
    notFromServer: '{emoji}此邀请不是来自此服务器。'
badges:
  shown: '{emoji}现在您的徽章将在消息中显示。'
  hidden: '{emoji}现在您的徽章将在消息中隐藏。'
  command:
    description: '🏅 配置您的首选显示徽章'
    options:
      show:
        name: '显示'
        description: '是否在消息中显示或隐藏您的徽章'
  list:
    developer: 'InterChat的核心开发者'
    staff: 'InterChat的工作人员'
    translator: 'InterChat的译者'
    voter: '在过去 12 小时内投票给InterChat'
global:
  webhookNoLongerExists: '{emoji}此频道的网络回调已不存在。若要继续使用 InterChat，请使用“／恢复连接”指令重新创建网络回调。'
  noReason: 未提供原因。
  noDesc: 无说明。
  version: InterChat版本{version}
  loading: '{emoji}我正在处理您的请求，请稍候……'
  reportOptionMoved: '{emoji}此选项已移动！如向中心监管员报告，请使用更新的“应用程序＞信息／报告”指令。如直接向InterChat工作人员报告，只需登录[支持服务器]({support_invite})并创建一张带有证明的票证。'
  private: '私人'
  public: '公开'
  yes: '是'
  no: '否'
  cancelled: '{emoji}已取消。未进行任何更改。'
warn:
  modal:
    title: 用户警告
    reason:
      label: 原因
      placeholder: 输入警告此用户的原因……
  success: |
    {emoji} **{name}**警告成功。

    -# 下次在中心发送消息时，他们将收到最新警告的通知。请勿同时发出多个警告。
  dm:
    title: '{emoji}警告通知'
    description: '您在中心{hubName}已被警告'
  log:
    title: '{emoji}被警告用户'
    description: |
      {arrow} **用户：** {user} ({userId})
      {arrow} **监管员：** {moderator} ({modId})
      {arrow} **原因：** {reason}
    footer: '警告来自：{moderator}'
