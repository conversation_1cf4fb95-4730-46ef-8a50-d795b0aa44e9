p-- CreateTable
CREATE TABLE "Achievement" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "badgeEmoji" TEXT NOT NULL,
    "badgeUrl" TEXT,
    "threshold" INTEGER NOT NULL DEFAULT 1,
    "secret" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Achievement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserAchievement" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "achievementId" TEXT NOT NULL,
    "unlockedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserAchievement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserAchievementProgress" (
    "userId" TEXT NOT NULL,
    "achievementId" TEXT NOT NULL,
    "currentValue" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserAchievementProgress_pkey" PRIMARY KEY ("userId","achievementId")
);

-- CreateIndex
CREATE UNIQUE INDEX "Achievement_id_key" ON "Achievement"("id");

-- CreateIndex
CREATE UNIQUE INDEX "UserAchievement_userId_achievementId_key" ON "UserAchievement"("userId", "achievementId");

-- CreateIndex
CREATE INDEX "UserAchievement_userId_idx" ON "UserAchievement"("userId");

-- CreateIndex
CREATE INDEX "UserAchievement_achievementId_idx" ON "UserAchievement"("achievementId");

-- AddForeignKey
ALTER TABLE "UserAchievement" ADD CONSTRAINT "UserAchievement_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAchievement" ADD CONSTRAINT "UserAchievement_achievementId_fkey" FOREIGN KEY ("achievementId") REFERENCES "Achievement"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAchievementProgress" ADD CONSTRAINT "UserAchievementProgress_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserAchievementProgress" ADD CONSTRAINT "UserAchievementProgress_achievementId_fkey" FOREIGN KEY ("achievementId") REFERENCES "Achievement"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Insert base achievements
INSERT INTO "Achievement" ("id", "name", "description", "badgeEmoji", "badgeUrl", "threshold", "secret", "createdAt", "updatedAt") VALUES
('world-tour', 'World Tour', 'Chat in 10+ different servers across any hub', '🌎', 'https://i.imgur.com/GxUBGNF.png', 10, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('bridge-builder', 'Bridge Builder', 'Link your server to a hub for the first time', '🌉', 'https://i.imgur.com/JCMnRqx.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('global-chatter', 'Global Chatter', 'Send 100+ messages across all hubs', '💬', 'https://i.imgur.com/CjSHaYD.png', 100, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('message-marathoner', 'Message Marathoner', 'Send 1,000+ messages in total', '🏃', 'https://i.imgur.com/D8sFW7h.png', 1000, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('streak-master', 'Streak Master', 'Send messages for 30 consecutive days', '🔥', 'https://i.imgur.com/m7PgrHn.png', 30, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('cross-cultural-ambassador', 'Cross-Cultural Ambassador', 'Receive reactions from users in 5+ different servers', '🤝', 'https://i.imgur.com/xz0dSMj.png', 5, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('chain-reaction', 'Chain Reaction', 'Trigger a conversation with 10+ replies from different servers', '⛓️', 'https://i.imgur.com/qGu57pY.png', 10, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('social-butterfly', 'Social Butterfly', 'Have your message replied to by users in 5+ servers', '🦋', 'https://i.imgur.com/KfnJw3b.png', 5, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('welcome-wagon', 'Welcome Wagon', 'Be the first to greet a new server joining the hub', '👋', 'https://i.imgur.com/Olypviz.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('hub-hopper', 'Hub Hopper', 'Participate in 3+ different hubs', '🦘', 'https://i.imgur.com/9yKRIUB.png', 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('interconnected', 'Interconnected', 'Join a hub that connects 10+ servers', '🔄', 'https://i.imgur.com/e9epD7c.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('peacekeeper', 'Peacekeeper', 'Report an issue that moderators resolve, improving hub safety', '🛡️', 'https://i.imgur.com/xNkFEu0.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('hub-guardian', 'Hub Guardian', 'Be a moderator in a hub with 90%+ positive interactions', '👮', 'https://i.imgur.com/pM1C1vA.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('hub-hero', 'Hub Hero', 'Answer 50+ help requests or FAQs in the hub', '🦸', 'https://i.imgur.com/qPrptEw.png', 50, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('pioneer', 'Pioneer', 'Be among the first 100 users globally to join InterChat', '🚀', 'https://i.imgur.com/Ekgv8hI.png', 1, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('echo-chamber', 'Echo Chamber', 'Your message is broadcast to 10+ servers in one hub', '📢', 'https://i.imgur.com/Pg2TjCe.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('intercompletionist', 'InterCompletionist', 'Unlock all other achievements', '⭐', 'https://i.imgur.com/SX3cRJm.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('golden-webhook', 'Golden Webhook', 'Celebrate InterChat''s anniversary by being active during its birthday month', '🎂', 'https://i.imgur.com/AzBu41C.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('archive-explorer', 'Archive Explorer', 'View the oldest archived message in a hub', '🗿', 'https://i.imgur.com/rzQqswR.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('bridge-booster', 'Bridge Booster', 'Help troubleshoot a hub connection issue validated by moderators', '🔧', 'https://i.imgur.com/PZhQmfN.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('polyglot', 'Polyglot', 'Use the bot in 3+ languages', '🗣️', 'https://i.imgur.com/kXH2ium.png', 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('voter', 'Voter', 'Vote 10 times on Top.gg', '🗳️', 'https://i.imgur.com/gLaclnC.png', 10, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('super-voter', 'Super Voter', 'Vote 100 times on Top.gg', '🌟', 'https://i.imgur.com/dQtGqd4.png', 100, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('hub-creator', 'Hub Creator', 'Create a hub', '🏙️', 'https://i.imgur.com/7TyxoHe.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('viral-hub', 'Viral Hub', 'Get more than 25 servers in your hub', '🌐', 'https://i.imgur.com/YL2eWUQ.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('hub-empire', 'Hub Empire', 'Achieve 100 servers in your hub', '👑', 'https://i.imgur.com/Pr39xIO.png', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
