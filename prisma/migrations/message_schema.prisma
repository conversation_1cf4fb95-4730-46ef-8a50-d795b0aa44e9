// New models for message data migration from Redis to PostgreSQL

model Message {
  id            String    @id // Discord message ID (Snowflake)
  hubId         String
  hub           Hub       @relation(fields: [hubId], references: [id], onDelete: Cascade)
  content       String
  imageUrl      String?
  channelId     String
  guildId       String
  authorId      String
  timestamp     DateTime
  reactions     Json?     // Stored as JSON object
  referredMessageId String?
  createdAt     DateTime  @default(now())
  
  // Relations
  broadcasts    Broadcast[]
  referredBy    Message[]  @relation("MessageReference")
  referredTo    Message?   @relation("MessageReference", fields: [referredMessageId], references: [id])

  @@index([hubId])
  @@index([channelId])
  @@index([authorId])
  @@index([timestamp]) // For message cleanup by age
  @@index([referredMessageId])
}

model Broadcast {
  id            String    @id @default(cuid())
  messageId     String    // Original message ID
  message       Message   @relation(fields: [messageId], references: [id], onDelete: Cascade)
  broadcastMessageId String // Discord message ID of the broadcast message
  channelId     String
  mode          Int       // Connection mode
  createdAt     DateTime  @default(now())

  @@unique([broadcastMessageId])
  @@index([messageId])
  @@index([channelId])
}
