generator client {
  provider     = "prisma-client-js"
  output       = "../build/generated/prisma-mongo/client"
  moduleFormat = "esm"
}

datasource db {
  provider = "mongodb"
  url      = env("MONGODB_URL")
}

enum Role {
  MODERATOR
  MANAGER
}

enum InfractionType {
  BLACKLIST
  WARNING
}

enum InfractionStatus {
  ACTIVE
  REVOKED
  APPEALED
}

enum AppealStatus {
  PENDING
  ACCEPTED
  REJECTED
}

enum BlockWordAction {
  BLOCK_MESSAGE
  BLACKLIST
  SEND_ALERT
}

type LogChannel {
  channelId String
  roleId    String?
}

// Models
model Hub {
  id                  String               @id @default(auto()) @map("_id") @db.ObjectId
  name                String
  description         String
  owner               User                 @relation(fields: [ownerId], references: [id])
  ownerId             String
  iconUrl             String
  bannerUrl           String?
  welcomeMessage      String?
  private             Boolean              @default(true)
  locked              Boolean              @default(false)
  appealCooldownHours Int                  @default(168)
  lastActive          DateTime             @default(now())
  settings            Int
  rules               String[]             @default([])
  rulesAcceptances    HubRulesAcceptance[]
  moderators          HubModerator[]
  connections         Connection[]
  upvotes             HubUpvote[]
  reviews             HubReview[]
  logConfig           HubLogConfig?
  blockWords          BlockWord[]
  infractions         Infraction[] // Combined user and server infractions
  invites             HubInvite[]
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt

  @@unique([name])
  @@index([ownerId])
}

model Tag {
  id    String @id @default(auto()) @map("_id") @db.ObjectId
  name  String
  count Int    @default(0)
  // hubIds Hub[]
}

model HubUpvote {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  hubId     String   @db.ObjectId
  userId    String
  createdAt DateTime @default(now())
  hub       Hub      @relation(fields: [hubId], references: [id])
  user      User     @relation(fields: [userId], references: [id])

  @@unique([hubId, userId])
  @@index([hubId])
}

model HubReview {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  hubId     String   @db.ObjectId
  userId    String
  rating    Int
  text      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  hub       Hub      @relation(fields: [hubId], references: [id])
  user      User     @relation(fields: [userId], references: [id])

  @@unique([hubId, userId])
  @@index([hubId])
}

model HubModerator {
  id     String @id @default(auto()) @map("_id") @db.ObjectId
  hubId  String @db.ObjectId
  userId String
  role   Role
  hub    Hub    @relation(fields: [hubId], references: [id])
  user   User   @relation(fields: [userId], references: [id])

  @@unique([hubId, userId])
}

model Connection {
  id                   String      @id @default(auto()) @map("_id") @db.ObjectId
  channelId            String      @unique
  parentId             String? // Parent channel ID for threads
  serverId             String
  hubId                String      @db.ObjectId
  connected            Boolean     @default(true)
  compact              Boolean     @default(false)
  invite               String?
  createdAt            DateTime    @default(now())
  embedColor           String?
  webhookURL           String
  lastActive           DateTime    @default(now())
  joinRequestsDisabled Boolean?    @default(false)
  hub                  Hub         @relation(fields: [hubId], references: [id])
  server               ServerData? @relation(fields: [serverId], references: [id])

  @@unique([channelId, serverId])
  @@unique([hubId, serverId])
  @@index([hubId, channelId])
}

model Infraction {
  id          String           @id @default(nanoid(10)) @map("_id")
  hubId       String           @db.ObjectId
  type        InfractionType   @default(BLACKLIST)
  status      InfractionStatus @default(ACTIVE)
  moderator   User             @relation(name: "issuedInfractions", fields: [moderatorId], references: [id])
  moderatorId String
  reason      String
  expiresAt   DateTime?
  notified    Boolean          @default(false)

  // For user infractions
  user   User?   @relation(name: "infractions", fields: [userId], references: [id])
  userId String?

  // For server infractions
  serverId   String?
  serverName String?

  hub       Hub      @relation(fields: [hubId], references: [id])
  appeals   Appeal[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([status, hubId])
}

model Appeal {
  id           String       @id @default(auto()) @map("_id") @db.ObjectId
  infractionId String
  userId       String
  reason       String
  status       AppealStatus @default(PENDING)
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  infraction   Infraction   @relation(fields: [infractionId], references: [id])
  user         User         @relation(fields: [userId], references: [id])

  @@index([infractionId])
  @@index([userId])
  @@index([status])
}

model BlockWord {
  id        String            @id @default(auto()) @map("_id") @db.ObjectId
  hubId     String            @db.ObjectId
  name      String
  words     String // words separated by comma
  createdBy String
  actions   BlockWordAction[]
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt

  hub Hub @relation(fields: [hubId], references: [id])

  @@index([hubId])
  @@index([words])
}

model HubLogConfig {
  id            String      @id @default(auto()) @map("_id") @db.ObjectId
  hubId         String      @unique @db.ObjectId
  modLogs       LogChannel?
  joinLeaves    LogChannel?
  appeals       LogChannel?
  reports       LogChannel?
  networkAlerts LogChannel?
  hub           Hub         @relation(fields: [hubId], references: [id])
}

model HubInvite {
  code    String   @id @default(nanoid(10)) @map("_id")
  hubId   String   @db.ObjectId
  expires DateTime
  hub     Hub      @relation(fields: [hubId], references: [id])

  @@index([hubId])
}

model HubRulesAcceptance {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  userId     String
  acceptedAt DateTime @default(now())
  hub        Hub      @relation(fields: [hubId], references: [id])
  hubId      String   @db.ObjectId

  @@unique([userId, hubId])
  @@index([userId])
  @@index([hubId])
}

model User {
  id                String          @id @map("_id")
  name              String?
  showBadges        Boolean         @default(true)
  image             String?
  locale            String?
  voteCount         Int          @default(0)
  reputation        Int          @default(0)
  lastVoted         DateTime?
  banReason         String?
  mentionOnReply    Boolean         @default(true)
  acceptedRules     Boolean         @default(false)
  messageCount      Int          @default(0)
  lastMessageAt     DateTime        @default(now())
  modPositions      HubModerator[]
  ownedHubs         Hub[]
  infractions       Infraction[]    @relation("infractions")
  appeals           Appeal[]
  reviews           HubReview[]
  issuedInfractions Infraction[]    @relation("issuedInfractions")
  upvotedHubs       HubUpvote[]
  inboxLastReadDate DateTime?       @default(now())
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  ReputationLog     ReputationLog[]
}

model Announcement {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  title        String
  content      String
  thumbnailUrl String?
  imageUrl     String?
  createdAt    DateTime @default(now())
}

model ServerData {
  id            String       @id @map("_id")
  name          String?
  iconUrl       String?
  premiumStatus Boolean      @default(false)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  inviteCode    String?
  messageCount  Int          @default(0)
  lastMessageAt DateTime     @default(now())
  connections   Connection[]
}

enum CallRatingStatus {
  like
  dislike
}

model CallRating {
  id        String           @id @default(auto()) @map("_id") @db.ObjectId
  callId    String
  targetId  String
  raterId   String
  rating    CallRatingStatus
  timestamp DateTime         @default(now())
}

model ReputationLog {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  giverId    String
  receiver   User     @relation(fields: [receiverId], references: [id])
  receiverId String
  timestamp  DateTime @default(now())
  reason     String
  automatic  Boolean  @default(false)
}
