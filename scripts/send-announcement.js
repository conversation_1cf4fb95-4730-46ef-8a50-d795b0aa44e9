// @ts-check

/**
 * One-time script to send InterChat v5.0.0 announcement using webhook with Components V2
 *
 * Usage:
 * node scripts/send-announcement.js WEBHOOK_URL
 */

import {
  WebhookClient,
  TextDisplayBuilder,
  SeparatorBuilder,
  SeparatorSpacingSize,
  MediaGalleryBuilder,
  MediaGalleryItemBuilder,
  MessageFlags,
} from 'discord.js';
import { stripIndents } from 'common-tags';

// Check for webhook URL
const webhookUrl = process.argv[2];

if (!webhookUrl) {
  console.error('Please provide a webhook URL as an argument');
  console.error('Usage: node scripts/send-announcement.js WEBHOOK_URL');
  process.exit(1);
}

/**
 * @param {string} url
 * @param {string} [description]
 */
function bannerItem(url, description) {
  return new MediaGalleryItemBuilder().setURL(url).setDescription(description ?? url);
}

async function sendAnnouncement(webhookUrl) {
  // Create webhook client
  const webhook = new WebhookClient({ url: webhookUrl });

  try {
    // Header section with title
    const headerText = new TextDisplayBuilder().setContent(
      stripIndents`
      # InterChat v5.0.0: The Next Generation of Cross-Community Communication
      *The most powerful update yet—made with performance, safety, and usability in mind.*
      `,
    );

    // Separator after header
    const separator = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Large);
    const separatorSmall = new SeparatorBuilder().setSpacing(SeparatorSpacingSize.Small);

    const bannerGallery = new MediaGalleryBuilder().addItems(
      bannerItem(
        'https://media.discordapp.net/attachments/1281098719353241644/1374191927150379099/dash_home.png?ex=682d2776&is=682bd5f6&hm=050b40eae92643af84ccc4fc60b6314224fd6e59311e1c37c0121613ee689909&=&format=webp&quality=lossless&size=4096',
      ),
    );

    // Features Section - Dashboard
    const dashboardSection = new TextDisplayBuilder().setContent(
      stripIndents`
      ## Key Features & Improvements

      ## **🚀 Next-Generation Dashboard**  
      
      Unify your community management with our **redesigned dashboard**, now featuring hub configuration, server management, connection viewer and anti-swear customization. [Explore the Dashboard →](https://interchat.tech/dashboard)
      `,
    );

    const dashboardGallery = new MediaGalleryBuilder().addItems(
      bannerItem(
        'https://media.discordapp.net/attachments/1281098719353241644/1374191926395539496/hubs_page.png?ex=682d2776&is=682bd5f6&hm=1e964419836b0030d7cdb41da447ad195ab12e6f9298ff0efb08603864085c6c&=&format=webp&quality=lossless&size=4096',
      ),
      bannerItem(
        'https://media.discordapp.net/attachments/1281098719353241644/1374191926781411390/servers_page.png?ex=682d2776&is=682bd5f6&hm=cef37f6d1676b205690d997008df0164b86b389e0d0d3c66ff12f3e2345c46f5&=&format=webp&quality=lossless&size=4096',
      ),
      bannerItem(
        'https://media.discordapp.net/attachments/1281098719353241644/1374191926034567252/hub.png?ex=682d2776&is=682bd5f6&hm=ed98d34b291c952f07b289c9a6d939b7e2abaf33ff61ca29167bd64fce5d4974&=&format=webp&quality=lossless&size=4096',
      ),
    );

    // Features Section - Performance
    const performanceSection = new TextDisplayBuilder().setContent(
      stripIndents`
      ## **⚡ Performance Breakthroughs**  
      
      - **300% faster message processing** (700ms → 150ms)  
      - Optimized caching architecture for reduced latency  
      - Full migration to **PostgreSQL** for scalability and advanced querying
        *Self-hosters: Follow our [Migration Guide](https://interchat.tech/docs/self-hosting/postgresql-migration) for quick transitions using a script.*
      `,
    );

    // Features Section - Global Accessibility
    const accessibilitySection = new TextDisplayBuilder().setContent(
      stripIndents`
      ## **🌍 Global Accessibility**  
      
      - **New languages**: Spanish, Russian, Estonian  
      - Revamped **Hub Directory** with mobile-first design and future tag-based discovery
      `,
    );

    const accessibilityGallery = new MediaGalleryBuilder().addItems(
      bannerItem(
        'https://media.discordapp.net/attachments/1281098719353241644/1374194975956271185/image.png?ex=682d2a4d&is=682bd8cd&hm=8be3a4a5aefc387458b9f92a55f91d7a3678ace0f3a2614bb7ef060f69d93a6b&=&format=webp&quality=lossless&size=4096',
      ),
    );

    // Features Section - Safety & Moderation
    const safetySection = new TextDisplayBuilder().setContent(
      stripIndents`
      ## **🛡️ Advanced Safety & Moderation**  
      
      - Faster **anti-swear filters** (300ms → 100ms (or even 10-15ms with caching))  
      - New \`/warn\` command for moderators  
      - Better moderation controls via dashboard
      `,
    );
    const safetyGallery = new MediaGalleryBuilder().addItems(
      bannerItem(
        'https://cdn.discordapp.com/attachments/1281098719353241644/1374199766325399644/asd.png?ex=682d2ec3&is=682bdd43&hm=b95a2947916151f767e55b2ef2b96a4be0189ff7b745b37c72ca29223b1c6427&size=4096',
      ),
    );

    // Features Section - User Experience
    const uxSection = new TextDisplayBuilder().setContent(
      stripIndents`
      ## **✨ Enhanced User Experience**  
      
      - **Interactive tutorials** (\`/tutorial\`) for onboarding  
      - Persistent messages, reaction systems, and **user badges**
      - Discord Components V2 integration for interactive UI
      `,
    );

    const uxGallery = new MediaGalleryBuilder().addItems(
      bannerItem(
        'https://cdn.discordapp.com/attachments/1281098719353241644/1374195526819643432/asd.png?ex=682d2ad0&is=682bd950&hm=be71a0152d899716988496941d0dd89461f8271d6b19e20cc6a28e58d1b4b09c&size=4096',
      ),
      bannerItem(
        'https://cdn.discordapp.com/attachments/1281098719353241644/1374197936916926525/asd.png?ex=682d2d0f&is=682bdb8f&hm=11a6ef9ec1c3c744ceaeddced8338c04705dde514dd58c1261382ac228bee994&size=4096',
      ),
    );

    // Features Section - Community Engagement
    const communitySection = new TextDisplayBuilder().setContent(
      stripIndents`
      ## **🎮 Community Engagement**  
      
      - **Monthly leaderboards** (\`/leaderboard\`) for active hubs  
      - **Voter perks**: Exclusive badges and features (use \`/vote\`)  
      - **Call system**: \`/call\`, \`/hangup\`, \`/skip\` for 1-to-1 server connections
      `,
    );

    const communityGallery = new MediaGalleryBuilder().addItems(
      bannerItem(
        'https://cdn.discordapp.com/attachments/1281098719353241644/1374198692898279555/asd.png?ex=682d2dc3&is=682bdc43&hm=96079465f6b3f1f9dc8a9c54eba708b48abe873a2cbd95d00487a250cae71f11&size=4096',
      ),
      bannerItem(
        'https://cdn.discordapp.com/attachments/1281098719353241644/1374198552560930837/asd.png?ex=682d2da2&is=682bdc22&hm=5693eb307200faaf1d4f561abf23e79cf59213fbd4aa3c9e5000deee144720ef&size=4096',
      ),
    );

    // Developer Section
    const devSection = new TextDisplayBuilder().setContent(
      stripIndents`
      ## **For Developers & Self-Hosters**  
      
      - **PostgreSQL migration** for scalability and advanced querying  
      - Open-source documentation updates for seamless deployment
      `,
    );

    // Future Section
    const futureSection = new TextDisplayBuilder().setContent(
      stripIndents`
      ## **What's Next?**  
      
      - **Custom hub badges** (coming in v5.1)  
      - **Tag-based hub discovery** in the directory  
      - *Your feedback shapes our roadmap! Share ideas in the <#1363495957336948797> channel of our [Support Server](https://discord.gg/cgYgC6YZyX).*
      `,
    );

    // Getting Started Section
    const gettingStartedSection = new TextDisplayBuilder().setContent(
      stripIndents`
      ## **Getting Started**  
      
      1. Update bot permissions if prompted  
      2. Explore the new dashboard: [interchat.tech/dashboard](https://interchat.tech/dashboard)  
      3. Run \`/tutorial\` for guided feature tours  
      4. Review \`/help\` for command updates
      `,
    );

    // Footer Section
    const footerSection = new TextDisplayBuilder().setContent(
      stripIndents`
      *InterChat Team*  
      *Empowering communities since 2020*  
      
      **Official Links**  
      📚 [Documentation](https://interchat.tech/docs) | 🗂️ [Hub Directory](https://interchat.tech/hubs) | 🐞 [Report Issues](https://interchat.tech/support)  
      
      *Version 5.0.0 marks a new era—thank you for being part of the journey.*
      `,
    );

    // Send the message with all components
    const sentMessage = await webhook.send({
      username: 'InterChat Updates',
      // avatarURL: 'https://i.imgur.com/YourBotAvatar.png', // Replace with your actual avatar URL
      components: [
        headerText,
        separator,
        bannerGallery,
        dashboardSection,
        separatorSmall,
        dashboardGallery,
        separatorSmall,
        accessibilitySection,
        separatorSmall,
        accessibilityGallery,
        separatorSmall,
        safetySection,
        separatorSmall,
        safetyGallery,
        separatorSmall,
        uxSection,
        separatorSmall,
        uxGallery,
        separatorSmall,
        communitySection,
        separatorSmall,
        communityGallery,
        separatorSmall,
        performanceSection,
        separator,
        devSection,
        separator,
        futureSection,
        separator,
        gettingStartedSection,
        separator,
        footerSection,
      ],
      flags: [MessageFlags.IsComponentsV2],
      withComponents: true,
    });

    console.log('Announcement sent successfully!');
    console.log(`Message ID: ${sentMessage.id}`);
  } catch (error) {
    console.error('Failed to send announcement:', error);
  } finally {
    // Close the webhook client
    webhook.destroy();
  }
}

// Run the script
sendAnnouncement(webhookUrl).catch(console.error);
