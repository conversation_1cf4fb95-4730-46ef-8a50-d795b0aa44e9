/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import BaseCommand from '#src/core/BaseCommand.js';
import type Context from '#src/core/CommandContext/Context.js';
import AchievementService from '#src/services/AchievementService.js';
import { HubService } from '#src/services/HubService.js';
import db from '#src/utils/Db.js';
import { UIComponents } from '#src/utils/DesignSystem.js';
import { ApplicationCommandOptionType, AutocompleteInteraction } from 'discord.js';

/**
 * Command to check hub sentiment and update Hub Guardian achievement
 */
export default class HubSentimentCommand extends BaseCommand {
  constructor() {
    super({
      name: 'sentiment',
      description: '📊 Check hub sentiment and update Hub Guardian achievement',
      types: { slash: true },
      options: [
        {
          name: 'hub',
          description: 'The hub to check sentiment for',
          type: ApplicationCommandOptionType.String,
          required: true,
          autocomplete: true,
        },
      ],
      staffOnly: true, // Only staff can use this command
    });
  }

  async execute(ctx: Context): Promise<void> {
    await ctx.deferReply({ flags: ['Ephemeral'] });

    const hubId = ctx.options.getString('hub', true);

    // Get hub
    const hubService = new HubService();
    const hub = await hubService.fetchHub(hubId);

    if (!hub) {
      const ui = new UIComponents(ctx.client);
      const container = ui.createErrorMessage(
        'Hub Not Found',
        'The specified hub could not be found.',
      );
      await ctx.editReply({ components: [container], flags: ['IsComponentsV2'] });
      return;
    }

    // Calculate sentiment (this would normally use a real sentiment analysis service)
    // For demo purposes, we'll use a random positive percentage between 85% and 100%
    const positivePercentage = 85 + Math.floor(Math.random() * 15);

    // Update Hub Guardian achievement
    const achievementService = new AchievementService();
    await achievementService.updateHubGuardianAchievement(hubId, positivePercentage, ctx.client);

    // Create UI response
    const ui = new UIComponents(ctx.client);
    const container = ui.createSuccessMessage(
      'Hub Sentiment Analysis',
      `Hub: **${hub.data.name}**\nPositive Interaction Rate: **${positivePercentage}%**\n\nHub Guardian achievements have been updated for all moderators of this hub.`,
    );

    await ctx.editReply({ components: [container], flags: ['IsComponentsV2'] });
  }

  async autocomplete(interaction: AutocompleteInteraction): Promise<void> {
    const focused = interaction.options.getFocused(true);

    if (focused.name === 'hub') {
      const query = focused.value.toString().toLowerCase();

      // Get hubs matching the query
      const hubs = await db.hub.findMany({
        where: {
          name: { contains: query, mode: 'insensitive' },
        },
        take: 25,
        orderBy: { name: 'asc' },
      });

      await interaction.respond(
        hubs.map((hub) => ({
          name: hub.name,
          value: hub.id,
        })),
      );
    }
  }
}
