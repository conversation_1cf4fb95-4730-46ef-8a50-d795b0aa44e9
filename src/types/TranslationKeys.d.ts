/*
  THIS IS AN AUTOGENERATED FILE. DO NOT EDIT IT DIRECTLY.
  To regenerate this file, run 'npm run locale-types'.
*/

export type TranslationKeys = {
  'rules.header': never;
  'rules.botRulesNote': never;
  'rules.rules':
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link';
  'rules.welcome': 'emoji' | 'user';
  'rules.alreadyAccepted': 'emoji';
  'rules.continue': never;
  'rules.accept': never;
  'rules.decline': never;
  'rules.agreementNote': never;
  'rules.hubAgreementNote': never;
  'rules.accepted': 'emoji' | 'emoji' | 'support_invite' | 'donateLink';
  'rules.declined': 'emoji';
  'rules.hubAccepted': 'emoji';
  'rules.hubDeclined': 'emoji' | 'hubName';
  'rules.noHubRules': 'rules_link';
  'rules.hubRules': never;
  'rules.viewbotRules': never;
  'vote.description': never;
  'vote.footer': never;
  'vote.button.label': never;
  'vote.perks.moreComingSoon': 'support_invite';
  'network.accountTooNew': 'emoji' | 'user';
  'network.deleteSuccess': 'emoji' | 'user' | 'deleted' | 'total';
  'network.editSuccess': 'emoji' | 'user' | 'edited' | 'total';
  'network.editMessage': never;
  'network.newMessageContent': never;
  'network.editMessagePrompt': 'emoji';
  'network.editInProgress': 'emoji';
  'network.editInProgressError': 'emoji';
  'network.emptyContent': 'emoji';
  'network.onboarding.embed.title': 'hubName';
  'network.onboarding.embed.description': never;
  'network.onboarding.embed.footer': 'version';
  'network.onboarding.inProgress': 'emoji' | 'channel';
  'blacklist.success': 'emoji' | 'name';
  'blacklist.removed': 'emoji' | 'name';
  'blacklist.modal.reason.label': never;
  'blacklist.modal.reason.placeholder': never;
  'blacklist.modal.duration.label': never;
  'blacklist.modal.duration.placeholder': never;
  'blacklist.user.cannotBlacklistMod': 'emoji';
  'blacklist.user.alreadyBlacklisted': 'emoji';
  'blacklist.user.easterEggs.blacklistBot': never;
  'blacklist.server.alreadyBlacklisted': 'emoji';
  'blacklist.server.unknownError': 'server';
  'blacklist.list.user': 'id' | 'moderator' | 'reason' | 'expires';
  'blacklist.list.server': 'id' | 'moderator' | 'reason' | 'expires';
  'msgInfo.buttons.message': never;
  'msgInfo.buttons.server': never;
  'msgInfo.buttons.user': never;
  'msgInfo.buttons.report': never;
  'msgInfo.report.notEnabled': 'emoji';
  'msgInfo.report.success': 'emoji';
  invite: 'invite_emoji' | 'invite' | 'support_emoji' | 'support';
  'connection.joinRequestsDisabled': 'emoji';
  'connection.notFound': 'emoji';
  'connection.channelNotFound': 'emoji';
  'connection.alreadyConnected': 'emoji' | 'channel';
  'connection.switchChannel': 'emoji';
  'connection.switchCalled': 'emoji';
  'connection.switchSuccess': 'emoji' | 'channel';
  'connection.inviteRemoved': 'emoji';
  'connection.setInviteError': 'emoji';
  'connection.inviteAdded': 'emoji';
  'connection.emColorInvalid': 'emoji';
  'connection.emColorChange': 'emoji' | 'action';
  'connection.embed.title': never;
  'connection.embed.fields.hub': never;
  'connection.embed.fields.channel': never;
  'connection.embed.fields.invite': never;
  'connection.embed.fields.connected': never;
  'connection.embed.fields.emColor': never;
  'connection.embed.fields.compact': never;
  'connection.embed.footer': never;
  'connection.selects.placeholder': never;
  'connection.unpaused.desc': 'tick_emoji' | 'channel';
  'connection.unpaused.tips': 'pause_cmd' | 'edit_cmd';
  'connection.paused.desc': 'clock_emoji' | 'channel';
  'connection.paused.tips': 'unpause_cmd' | 'leave_cmd';
  'hub.notFound': 'emoji';
  'hub.notFound_mod': 'emoji';
  'hub.notManager': 'emoji';
  'hub.notModerator': 'emoji';
  'hub.notPrivate': 'emoji';
  'hub.notOwner': 'emoji';
  'hub.alreadyJoined': 'emoji' | 'hub' | 'channel';
  'hub.invalidChannel': 'emoji';
  'hub.invalidImgurUrl': 'emoji';
  'hub.join.success': 'hub' | 'channel';
  'hub.servers.total': 'from' | 'to' | 'total';
  'hub.servers.noConnections': 'emoji';
  'hub.servers.notConnected': 'emoji' | 'hub';
  'hub.servers.connectionInfo':
    | 'serverId'
    | 'channelName'
    | 'channelId'
    | 'joinedAt'
    | 'invite'
    | 'connected';
  'hub.blockwords.deleted': 'emoji';
  'hub.blockwords.notFound': 'emoji';
  'hub.blockwords.maxRules': 'emoji';
  'hub.blockwords.configure': 'rule';
  'hub.blockwords.actionsUpdated': 'emoji' | 'actions';
  'hub.blockwords.selectRuleToEdit': never;
  'hub.blockwords.listDescription': 'emoji' | 'totalRules';
  'hub.blockwords.listFooter': never;
  'hub.blockwords.ruleDescription': 'emoji' | 'ruleName' | 'words';
  'hub.blockwords.ruleFooter': never;
  'hub.blockwords.actionSelectPlaceholder': never;
  'hub.blockwords.embedFields.noActions': 'emoji';
  'hub.blockwords.embedFields.actionsName': never;
  'hub.blockwords.embedFields.actionsValue': 'actions';
  'hub.blockwords.modal.addRule': never;
  'hub.blockwords.modal.editingRule': never;
  'hub.blockwords.modal.ruleNameLabel': never;
  'hub.blockwords.modal.wordsLabel': never;
  'hub.blockwords.modal.wordsPlaceholder': never;
  'hub.blockwords.validating': 'emoji';
  'hub.blockwords.noRules': 'emoji';
  'hub.create.modal.title': never;
  'hub.create.modal.name.label': never;
  'hub.create.modal.name.placeholder': never;
  'hub.create.modal.description.label': never;
  'hub.create.modal.description.placeholder': never;
  'hub.create.modal.icon.label': never;
  'hub.create.modal.icon.placeholder': never;
  'hub.create.modal.banner.label': never;
  'hub.create.modal.banner.placeholder': never;
  'hub.create.maxHubs': 'emoji' | 'voteUrl' | 'maxHubs';
  'hub.create.invalidName': 'emoji';
  'hub.create.nameTaken': 'emoji';
  'hub.create.success': 'name' | 'support_invite' | 'donateLink';
  'hub.delete.confirm': 'hub';
  'hub.delete.ownerOnly': 'emoji';
  'hub.delete.success': 'emoji' | 'hub';
  'hub.delete.cancelled': 'emoji';
  'hub.browse.joinConfirm': 'hub' | 'channel';
  'hub.browse.joinFooter': never;
  'hub.browse.noHubs': 'emoji';
  'hub.browse.rating.invalid': never;
  'hub.browse.rating.success': never;
  'hub.invite.create.success': 'inviteCode' | 'expiry' | 'inviteCode';
  'hub.invite.revoke.invalidCode': 'emoji';
  'hub.invite.revoke.success': 'emoji' | 'inviteCode';
  'hub.invite.list.title': never;
  'hub.invite.list.noInvites': 'emoji';
  'hub.invite.list.notPrivate': 'emoji';
  'hub.joined.noJoinedHubs': 'emoji';
  'hub.joined.joinedHubs': 'total';
  'hub.leave.noHub': 'emoji';
  'hub.leave.confirm': 'hub' | 'channel';
  'hub.leave.confirmFooter': never;
  'hub.leave.success': 'emoji' | 'channel';
  'hub.moderator.noModerators': 'emoji';
  'hub.moderator.add.success': 'emoji' | 'user' | 'position';
  'hub.moderator.add.alreadyModerator': 'emoji' | 'user';
  'hub.moderator.remove.success': 'emoji' | 'user';
  'hub.moderator.remove.notModerator': 'emoji' | 'user';
  'hub.moderator.remove.notOwner': 'emoji';
  'hub.moderator.update.success': 'emoji' | 'user' | 'position';
  'hub.moderator.update.notModerator': 'emoji' | 'user';
  'hub.moderator.update.notAllowed': 'emoji';
  'hub.moderator.update.notOwner': 'emoji';
  'hub.manage.dashboardTip': 'url';
  'hub.manage.enterImgurUrl': never;
  'hub.manage.icon.changed': never;
  'hub.manage.icon.modal.title': never;
  'hub.manage.icon.modal.label': never;
  'hub.manage.icon.selects.label': never;
  'hub.manage.icon.selects.description': never;
  'hub.manage.description.changed': never;
  'hub.manage.description.modal.title': never;
  'hub.manage.description.modal.label': never;
  'hub.manage.description.modal.placeholder': never;
  'hub.manage.description.selects.label': never;
  'hub.manage.description.selects.description': never;
  'hub.manage.banner.changed': never;
  'hub.manage.banner.removed': never;
  'hub.manage.banner.modal.title': never;
  'hub.manage.banner.modal.label': never;
  'hub.manage.banner.selects.label': never;
  'hub.manage.banner.selects.description': never;
  'hub.manage.visibility.success': 'emoji' | 'visibility';
  'hub.manage.visibility.selects.label': never;
  'hub.manage.visibility.selects.description': never;
  'hub.manage.toggleLock.selects.label': never;
  'hub.manage.toggleLock.selects.description': never;
  'hub.manage.toggleLock.confirmation': 'status';
  'hub.manage.toggleLock.announcementTitle': 'status';
  'hub.manage.toggleLock.announcementDescription.locked': never;
  'hub.manage.toggleLock.announcementDescription.unlocked': never;
  'hub.manage.embed.visibility': never;
  'hub.manage.embed.connections': never;
  'hub.manage.embed.chatsLocked': never;
  'hub.manage.embed.blacklists': never;
  'hub.manage.embed.total': never;
  'hub.manage.embed.users': never;
  'hub.manage.embed.servers': never;
  'hub.manage.embed.hubStats': never;
  'hub.manage.embed.moderators': never;
  'hub.manage.embed.owner': never;
  'hub.manage.logs.title': never;
  'hub.manage.logs.reset': 'emoji' | 'type';
  'hub.manage.logs.roleSuccess': 'emoji' | 'type' | 'role';
  'hub.manage.logs.roleRemoved': 'emoji' | 'type';
  'hub.manage.logs.channelSuccess': 'emoji' | 'type' | 'channel';
  'hub.manage.logs.channelSelect': never;
  'hub.manage.logs.roleSelect': never;
  'hub.manage.logs.reportChannelFirst': 'emoji';
  'hub.manage.logs.config.title': 'type';
  'hub.manage.logs.config.description': 'arrow' | 'arrow';
  'hub.manage.logs.config.fields.channel': never;
  'hub.manage.logs.config.fields.role': never;
  'hub.manage.logs.reports.label': never;
  'hub.manage.logs.reports.description': never;
  'hub.manage.logs.modLogs.label': never;
  'hub.manage.logs.modLogs.description': never;
  'hub.manage.logs.joinLeaves.label': never;
  'hub.manage.logs.joinLeaves.description': never;
  'hub.manage.logs.appeals.label': never;
  'hub.manage.logs.appeals.description': never;
  'hub.manage.logs.networkAlerts.label': never;
  'hub.manage.logs.networkAlerts.description': never;
  'hub.transfer.invalidUser': 'emoji';
  'hub.transfer.selfTransfer': 'emoji';
  'hub.transfer.botUser': 'emoji';
  'hub.transfer.confirm': 'hub' | 'newOwner';
  'hub.transfer.cancelled': 'emoji';
  'hub.transfer.error': 'emoji';
  'hub.transfer.success': 'emoji' | 'hub' | 'newOwner';
  'hub.transfer.timeout': 'emoji';
  'hub.rules.noRules': 'emoji';
  'hub.rules.list': 'emoji' | 'rules';
  'hub.rules.maxRulesReached': 'emoji' | 'max';
  'hub.rules.ruleExists': 'emoji';
  'hub.rules.selectedRule': 'number';
  'hub.rules.modal.add.title': never;
  'hub.rules.modal.add.label': never;
  'hub.rules.modal.add.placeholder': never;
  'hub.rules.modal.edit.title': never;
  'hub.rules.modal.edit.label': never;
  'hub.rules.modal.edit.placeholder': never;
  'hub.rules.select.placeholder': never;
  'hub.rules.select.option.label': 'number';
  'hub.rules.buttons.add': never;
  'hub.rules.buttons.edit': never;
  'hub.rules.buttons.delete': never;
  'hub.rules.buttons.back': never;
  'hub.rules.success.add': 'emoji';
  'hub.rules.success.edit': 'emoji';
  'hub.rules.success.delete': 'emoji';
  'hub.rules.view.title': 'number';
  'hub.rules.view.select': never;
  'hub.welcome.set': 'emoji';
  'hub.welcome.removed': 'emoji';
  'hub.welcome.voterOnly': 'emoji';
  'hub.welcome.placeholder': 'user' | 'serverName' | 'hubName' | 'memberCount' | 'totalConnections';
  'report.modal.title': never;
  'report.modal.other.label': never;
  'report.modal.other.placeholder': never;
  'report.modal.bug.input1.label': never;
  'report.modal.bug.input1.placeholder': never;
  'report.modal.bug.input2.label': never;
  'report.modal.bug.input2.placeholder': never;
  'report.reasons.spam': never;
  'report.reasons.advertising': never;
  'report.reasons.nsfw': never;
  'report.reasons.harassment': never;
  'report.reasons.hate_speech': never;
  'report.reasons.scam': never;
  'report.reasons.illegal': never;
  'report.reasons.personal_info': never;
  'report.reasons.impersonation': never;
  'report.reasons.breaks_hub_rules': never;
  'report.reasons.trolling': never;
  'report.reasons.misinformation': never;
  'report.reasons.gore_violence': never;
  'report.reasons.raid_organizing': never;
  'report.reasons.underage': never;
  'report.dropdown.placeholder': never;
  'report.submitted': 'emoji' | 'support_command';
  'report.bug.title': never;
  'report.bug.affected': never;
  'report.bug.description': never;
  'language.set': 'lang';
  'setup.progress': 'current' | 'total';
  'setup.navigation.back': never;
  'setup.navigation.next': never;
  'setup.welcome.title': 'emoji';
  'setup.welcome.description': never;
  'setup.welcome.what_is_title': never;
  'setup.welcome.what_is_description': never;
  'setup.welcome.process_title': never;
  'setup.welcome.process_description': never;
  'setup.welcome.step1': never;
  'setup.welcome.step2': never;
  'setup.welcome.step3': never;
  'setup.welcome.step4': never;
  'setup.welcome.step5': never;
  'setup.welcome.start_button': never;
  'setup.welcome.support_button': never;
  'setup.welcome.docs_button': never;
  'setup.language.title': never;
  'setup.language.description': never;
  'setup.language.select_placeholder': never;
  'setup.language.select_european': never;
  'setup.language.select_asian': never;
  'setup.language.european_languages': never;
  'setup.language.asian_languages': never;
  'setup.language.current_title': never;
  'setup.language.current_description': 'lang' | 'sample';
  'setup.language.note': never;
  'setup.language.english_description': never;
  'setup.language.spanish_description': never;
  'setup.language.chinese_description': never;
  'setup.language.russian_description': never;
  'setup.concepts.title': never;
  'setup.concepts.description': never;
  'setup.concepts.hubs.title': 'emoji';
  'setup.concepts.hubs.description': never;
  'setup.concepts.connections.title': 'emoji';
  'setup.concepts.connections.description': never;
  'setup.concepts.calls.title': 'emoji';
  'setup.concepts.calls.description': never;
  'setup.channel.title': never;
  'setup.channel.description': never;
  'setup.channel.select_placeholder': never;
  'setup.channel.tips_title': never;
  'setup.channel.tips_description': never;
  'setup.channel.tip1': never;
  'setup.channel.tip2': never;
  'setup.channel.tip3': never;
  'setup.hub.title': never;
  'setup.hub.description': never;
  'setup.hub.options_title': never;
  'setup.hub.options_description': never;
  'setup.hub.join_button': never;
  'setup.hub.create_button': never;
  'setup.hub.note': never;
  'setup.config.title': never;
  'setup.config.description': never;
  'setup.config.message_title': never;
  'setup.config.message_description': never;
  'setup.config.compact_button': never;
  'setup.config.moderation_title': never;
  'setup.config.moderation_description': never;
  'setup.complete.title': never;
  'setup.complete.description': 'hubName';
  'setup.complete.summary_title': never;
  'setup.complete.summary_description': never;
  'setup.complete.summary_channel': 'channel';
  'setup.complete.summary_hub': 'hubName';
  'setup.complete.next_steps_title': never;
  'setup.complete.next_steps_description': never;
  'setup.complete.next_step1': never;
  'setup.complete.next_step2': never;
  'setup.complete.next_step3': never;
  'setup.complete.finish_button': never;
  'setup.complete.tutorial_button': never;
  'setup.complete.support_button': never;
  'errors.messageNotSentOrExpired': 'emoji';
  'errors.notYourAction': 'emoji';
  'errors.notMessageAuthor': 'emoji';
  'errors.commandError': 'emoji' | 'support_invite' | 'errorId';
  'errors.mustVote': never;
  'errors.inviteLinks': 'emoji';
  'errors.invalidLangCode': 'emoji';
  'errors.unknownServer': 'emoji';
  'errors.unknownNetworkMessage': 'emoji';
  'errors.userNotFound': 'emoji';
  'errors.blacklisted': 'emoji' | 'hub';
  'errors.userBlacklisted': 'emoji';
  'errors.serverBlacklisted': 'emoji';
  'errors.serverNotBlacklisted': 'emoji';
  'errors.userNotBlacklisted': 'emoji';
  'errors.missingPermissions': 'emoji' | 'permissions';
  'errors.botMissingPermissions': 'emoji' | 'permissions';
  'errors.unknown': 'emoji' | 'support_invite';
  'errors.notUsable': 'emoji';
  'errors.cooldown': 'emoji' | 'time';
  'errors.serverNameInappropriate': 'emoji';
  'errors.banned': 'emoji' | 'support_invite';
  'config.setInvite.success': 'emoji';
  'config.setInvite.removed': 'emoji';
  'config.setInvite.invalid': 'emoji';
  'config.setInvite.notFromServer': 'emoji';
  'badges.shown': 'emoji';
  'badges.hidden': 'emoji';
  'badges.command.description': never;
  'badges.command.options.show.name': never;
  'badges.command.options.show.description': never;
  'badges.list.developer': never;
  'badges.list.staff': never;
  'badges.list.translator': never;
  'badges.list.voter': never;
  'global.webhookNoLongerExists': 'emoji';
  'global.noReason': never;
  'global.noDesc': never;
  'global.version': 'version';
  'global.loading': 'emoji';
  'global.reportOptionMoved': 'emoji' | 'support_invite';
  'global.private': never;
  'global.public': never;
  'global.yes': never;
  'global.no': never;
  'global.cancelled': 'emoji';
  'warn.modal.title': never;
  'warn.modal.reason.label': never;
  'warn.modal.reason.placeholder': never;
  'warn.success': 'emoji' | 'name';
  'warn.dm.title': 'emoji';
  'warn.dm.description': 'hubName';
  'warn.log.title': 'emoji';
  'warn.log.description':
    | 'arrow'
    | 'user'
    | 'userId'
    | 'arrow'
    | 'moderator'
    | 'modId'
    | 'arrow'
    | 'reason';
  'warn.log.footer': 'moderator';
};

export type ErrorLocaleKeys = Extract<keyof TranslationKeys, `errors.${string}`>;
