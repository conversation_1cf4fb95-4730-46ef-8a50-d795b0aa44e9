# InterChat: Connect Discord Communities Seamlessly

InterChat breaks down the walls between Discord servers, enabling real-time cross-server communication that builds thriving, engaged communities around shared interests.

## ✨ Standout Features

### 🌐 Powerful Hub System
Create or join themed communities where messages flow naturally between servers. Whether you're building a gaming network, educational community, or hobby group, InterChat hubs provide the infrastructure for meaningful cross-server connections.

### 📞 Direct Server Calls
Need a quick connection between just two servers? Our call feature lets you instantly connect channels for temporary collaborations without the commitment of joining a hub.

### 🛡️ Advanced Security & Moderation
Keep your cross-server conversations safe with smart content filtering, anti-spam protection, and comprehensive moderation tools that work across server boundaries.

### 🎮 Visual Dashboard
Manage your communities through our intuitive web interface. Create and configure hubs, track analytics, and monitor server connections all in one place.

### 🔧 Highly Customizable
Personalize your experience with custom welcome messages, hub rules, and flexible permission settings that put you in control of your community.

## 🚀 Getting Started

1. **Add InterChat to your server**: Click the "Invite" button above
2. **Run the setup wizard**: Type `/setup` to configure your first connection
    - **Or Manually Connect your channel**: Use `/connect` to link your channel to a hub
3. **Start chatting!** Messages will now flow between all servers in the hub

## ✨ Premium Features

[Vote for us](https://top.gg/bot/769921109209907241/vote) every 12 hours to unlock voter perks including increased message length, sticker support, and custom welcome messages!

## 🔗 Useful Links

- [Official Website](https://interchat.tech)
- [Documentation](https://interchat.tech/docs)
- [Support Server](https://discord.gg/cgYgC6YZyX)
- [Ko-fi Page](https://ko-fi.com/interchat)

Join thousands of communities already using InterChat to build connections that transcend server boundaries!
